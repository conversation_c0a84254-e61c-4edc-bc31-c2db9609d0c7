<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="唯一标识" prop="suppressSn">
        <el-input v-model="queryParams.suppressSn" placeholder="请输入唯一标识" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="时间" prop="publishTime">
        <el-date-picker clearable v-model="queryParams.publishTime" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="风险程度" prop="riskLevel">
        <el-input v-model="queryParams.riskLevel" placeholder="请输入风险程度" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="领域" prop="domain">
        <el-input v-model="queryParams.domain" placeholder="请输入领域" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectSn">
        <el-input v-model="queryParams.projectSn" placeholder="请输入项目编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="租户编码" prop="tenantSn">
        <el-input v-model="queryParams.tenantSn" placeholder="请输入租户编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="屏幕编码" prop="screenSn">
        <el-input v-model="queryParams.screenSn" placeholder="请输入屏幕编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="栏目编码" prop="columnSn">
        <el-input v-model="queryParams.columnSn" placeholder="请输入栏目编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['screen:suppress:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:suppress:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:suppress:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['screen:suppress:export']">导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          v-hasPermi="['screen:suppress:add']">打压风险导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImportSoftware"
          v-hasPermi="['screen:suppressSoftware:add']">相关软件导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImportPatent"
          v-hasPermi="['screen:suppressPatent:add']">相关专利导入</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="suppressList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" prop="suppressSn" width="150" />
      <el-table-column label="受压企业名称" prop="enterpriseName" width="300" show-overflow-tooltip />
      <el-table-column label="时间" align="center" prop="publishTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关键词" align="center" prop="keywords" width="150" show-overflow-tooltip />
      <el-table-column label="企业描述" prop="summary" width="300" show-overflow-tooltip />
      <!-- <el-table-column label="人工智能新闻" prop="news" width="300" show-overflow-tooltip /> -->
      <el-table-column label="内容观点" prop="viewpoint" width="300" show-overflow-tooltip />
      <el-table-column label="对企业的影响" prop="impact" width="300" show-overflow-tooltip />
      <el-table-column label="相关引文" prop="relatedCitations" width="300" show-overflow-tooltip />
      <el-table-column label="风险程度" align="center" prop="riskLevel" width="100" />
      <el-table-column label="领域" align="center" prop="domain" width="100" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目编码" align="center" prop="projectSn" />
      <el-table-column label="租户编码" align="center" prop="tenantSn" show-overflow-tooltip/>
      <el-table-column label="屏幕编码" align="center" prop="screenSn" />
      <el-table-column label="栏目编码" align="center" prop="columnSn" />
      <el-table-column label="操作" align="center" fixed="right" min-width="340" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:suppress:edit']">修改
          </el-button>
          <el-button link type="primary" icon="Edit" @click="handleSoftware(scope.row)">相关软件
          </el-button>
          <el-button link type="primary" icon="Edit" @click="handlePatent(scope.row)">相关专利
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:suppress:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改大屏-打压风险对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="suppressRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="唯一标识" prop="suppressSn">
          <el-input v-model="form.suppressSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="受压企业名称" prop="enterpriseName">
          <el-input v-model="form.enterpriseName" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="时间" prop="publishTime">
          <el-date-picker clearable v-model="form.publishTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="风险程度" prop="riskLevel">
            <el-select v-model="form.riskLevel" placeholder="请选择风险程度">
              <el-option label="较轻" value="较轻"></el-option>
              <el-option label="一般" value="一般"></el-option>
              <el-option label="严重" value="严重"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="企业描述" prop="summary">
          <editor :min-height="192" v-model="form.summary" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="人工智能新闻" prop="news">
          <editor :min-height="192" v-model="form.news" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="内容观点" prop="viewpoint">
          <editor :min-height="192" v-model="form.viewpoint" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="对企业的影响" prop="impact">
          <editor :min-height="192" v-model="form.impact" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="相关引文" prop="relatedCitations">
          <editor :min-height="192" v-model="form.relatedCitations" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="领域" prop="domain">
          <el-input v-model="form.domain" placeholder="请输入领域" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据源编码" prop="sourceSn">
              <el-input v-model="form.sourceSn" placeholder="请输入数据源编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据源名称" prop="sourceName">
              <el-input v-model="form.sourceName" placeholder="请输入数据源名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="userStore.roles.includes('admin')">
            <el-form-item label="租户编码" prop="tenantSn">
              <el-input v-model="form.tenantSn" placeholder="请输入租户编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="projectSn">
              <el-input v-model="form.projectSn" placeholder="请输入项目编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕编码" prop="screenSn">
              <el-input v-model="form.screenSn" placeholder="请输入屏幕编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="栏目编码" prop="columnSn">
              <el-input v-model="form.columnSn" placeholder="请输入栏目编码" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <suppressSoftware v-model:visible="suppressSoftwareVisible" :sn="suppressSoftwareSn"
      :visibleTitle="suppressSoftwareTitle" @closeSoftware="closeSoftware">
    </suppressSoftware>
    <suppressPatent v-model:visible="suppressPatentVisible" :sn="suppressPatentSn" :visibleTitle="suppressPatentTitle"
      @closePatent="closePatent">
    </suppressPatent>
  </div>
</template>

<script setup name="Suppress">
import { getToken } from "@/utils/auth";
import { addSuppress, delSuppress, getSuppress, listSuppress, updateSuppress } from "@/api/screen/suppress";
import suppressPatent from "../secondLevel/suppressPatent/index.vue";
import suppressSoftware from "../secondLevel/suppressSoftware/index.vue";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();

const suppressList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    suppressSn: null,
    enterpriseName: null,
    publishTime: null,
    keywords: null,
    summary: null,
    news: null,
    viewpoint: null,
    impact: null,
    relatedCitations: null,
    riskLevel: null,
    domain: null,
    sourceSn: null,
    sourceName: null,
    author: null,
    orderNum: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    projectSn: null,
    tenantSn: null,
    screenSn: null,
    columnSn: null
  },
  rules: {
    projectSn: [
      { required: true, message: "项目编码不能为空", trigger: "blur" }
    ],
    // tenantSn: [
    //   { required: true, message: "租户编码不能为空", trigger: "blur" }
    // ],
    screenSn: [
      { required: true, message: "屏幕编码不能为空", trigger: "blur" }
    ],
    columnSn: [
      { required: true, message: "栏目编码不能为空", trigger: "blur" }
    ]
  },
  suppressSoftwareVisible: false,
  suppressSoftwareSn: null,
  suppressSoftwareTitle: '',
  suppressPatentVisible: false,
  suppressPatentSn: null,
  suppressPatentTitle: '',
});

const { queryParams, form, rules, suppressSoftwareVisible, suppressSoftwareSn, suppressSoftwareTitle, suppressPatentVisible, suppressPatentSn, suppressPatentTitle } = toRefs(data);

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  businessType: null,
  headers: { Authorization: "Bearer " + getToken() },
  url: ''
});

/** 查询大屏-打压风险列表 */
function getList() {
  loading.value = true;
  listSuppress(queryParams.value).then(response => {
    suppressList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    suppressSn: null,
    enterpriseName: null,
    publishTime: null,
    keywords: null,
    summary: null,
    news: null,
    viewpoint: null,
    impact: null,
    relatedCitations: null,
    riskLevel: null,
    domain: null,
    sourceSn: null,
    sourceName: null,
    author: null,
    orderNum: null,
    status: null,
    delFlag: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    projectSn: null,
    tenantSn: null,
    screenSn: null,
    columnSn: null
  };
  proxy.resetForm("suppressRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加大屏-打压风险";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getSuppress(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改大屏-打压风险";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["suppressRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateSuppress(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSuppress(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  _ids = _ids.flatMap(item => {
    const found = suppressList.value.find(row => row.id === item)
    return found?.suppressSn ? [found.suppressSn] : []
  })
  proxy.$modal.confirm('是否确认删除大屏-打压风险编号为"' + _ids + '"的数据项？').then(function () {
    return delSuppress(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/suppress/export', {
    ...queryParams.value
  }, `suppress_${new Date().getTime()}.xlsx`)
}

const handleSoftware = (row) => {
  suppressSoftwareSn.value = row.suppressSn
  suppressSoftwareTitle.value = row.enterpriseName
  suppressSoftwareVisible.value = true
}

const closeSoftware = () => {
  suppressSoftwareVisible.value = false
}

const handlePatent = (row) => {
  suppressPatentSn.value = row.suppressSn
  suppressPatentTitle.value = row.enterpriseName
  suppressPatentVisible.value = true
}

const closePatent = () => {
  suppressPatentVisible.value = false
}

function handleImport() {
  upload.title = "打压风险导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/suppress/excelImport";
  upload.businessType = 100;
  upload.open = true;
};

function handleImportSoftware() {
  upload.title = "相关软件导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/suppressSoftware/excelImport";
  upload.businessType = 101;
  upload.open = true;
};

function handleImportPatent() {
  upload.title = "相关专利导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/suppressPatent/excelImport";
  upload.businessType = 102;
  upload.open = true;
};

function importTemplate() {
  proxy.download("xty-screen/suppress/downloadTemplate?businessType=" + upload.businessType, {
  }, `${upload.title}模版.xlsx`);
};

const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};

function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

getList();
</script>

<style scoped lang="scss">
.app-container {
  :deep(.el-popper) {
    max-width: 800px !important;
    line-height: 1.5 !important;
    white-space: normal !important;
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
</style>