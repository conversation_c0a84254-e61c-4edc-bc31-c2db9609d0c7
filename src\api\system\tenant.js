import request from '@/utils/request'

// 查询租户列表
export function listTenant(query) {
  return request({
    url: '/system/tenant/list',
    method: 'get',
    params: query
  })
}

// 查询租户详细
export function getTenant(tenantId) {
  return request({
    url: '/system/tenant/' + tenantId,
    method: 'get'
  })
}

// 新增租户
export function addTenant(data) {
  return request({
    url: '/system/tenant',
    method: 'post',
    data: data
  })
}

// 修改租户
export function updateTenant(data) {
  return request({
    url: '/system/tenant/edit',
    method: 'post',
    data: data
  })
}

// 删除租户
export function delTenant(data) {
  return request({
    url: '/system/tenant/remove',
    method: 'post',
    data: data
  })
}

// 获取地区信息
export function regionTree(query) {
  return request({
    url: '/system/region/regionTree',
    method: 'get',
    params: query
  })
}

// 修改租户
export function updateTenantEditStatus(data) {
  return request({
    url: '/system/tenant/editStatus',
    method: 'post',
    data: data
  })
}

// 获取默认菜单
export function getDefaultMenu(tenantId) {
  return request({
    url: `/system/menu/defaultMenu`,
    method: 'get',
  })
}

// 查询数据源分类列表
export function getListClassify(query) {
  return request({
    url: "/system/generic/classify/list",
    method: "get",
    params: query,
  });
}

// 获取租户数据源
export function getTenantAssignData(params) {
  return request({
    url: `/system/tenant/source/assignData`,
    method: 'get',
    params: params
  })
}

// 修改租户数据源
export function updateTenantSource(data) {
  return request({
    url: '/system/tenant/source',
    method: 'post',
    data: data
  })
}
// 删除租户数据源
export function removeTenantSource(data) {
  return request({
    url: '/system/tenant/source/remove',
    method: 'post',
    data: data
  })
}

// 修改权重
export function updateWeight(data) {
  return request({
    url: `/system/tenant/source/update/weight`,
    method: 'post',
    data: data
  })
}