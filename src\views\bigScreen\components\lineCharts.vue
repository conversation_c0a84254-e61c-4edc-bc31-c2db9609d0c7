<template>
  <div class="tableList">
    <div
      v-for="(item, index) in labelList"
      :key="index"
      :class="{ active: currentSelected === item.value }"
      @click="handleSelect(item)"
    >
      {{ item.name }}
    </div>
  </div>
  <div
    id="mainLine"
    style="width: 100%; height: 576px; padding: 10px 20px"
  ></div>
</template>

<script setup>
import * as echarts from "echarts";
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import { divisionTrend } from "../test";
import { wlaqAnalysisList } from "@/api/bigScreen/index";

const emits = defineEmits(["openArticleList"]);
const props = defineProps({
  sccenId: {
    type: Number,
    default: 1,
  },
});

var myChart = null;
const option = ref({});
const labelList = ref([]);
// const labelList = computed(() => {
//   if (props.sccenId === 1) {
//     return [
//       { value: 1, name: "政策风险" },
//       { value: 2, name: "技术风险" },
//       { value: 3, name: "安全风险" },
//       { value: 4, name: "伦理风险" },
//       { value: 5, name: "中美关系风险" },
//     ];
//   } else if (props.sccenId === 2) {
//     return [
//       { value: 1, name: "政策风险" },
//       { value: 2, name: "技术风险" },
//       { value: 3, name: "数据安全风险" },
//       { value: 4, name: "中美关系风险" },
//       { value: 5, name: "新兴技术衍生风险" },
//     ];
//   }
//   return [
//     { value: 1, name: "政策风险" },
//     { value: 2, name: "技术风险" },
//     { value: 3, name: "安全风险" },
//     { value: 4, name: "伦理风险" },
//     { value: 5, name: "中美关系风险" },
//   ];
// });
const valueObj = ref({ xData: [], yData: [] });
const currentSelected = ref(null);

const initChart = () => {
  option.value = {
    tooltip: {
      show: true,
      trigger: "axis",
      showContent: true,
      triggerOn: "mousemove",
      // axisPointer: {
      //   type: "shadow",
      // },
    },
    legend: {
      show: true,
      textStyle: { color: "#fff", fontSize: 14 },
      padding: [0, 0],
      x: "center",
      bottom: 0,
      icon: "path://M616.920615 567.296c43.795692 0 87.591385-34.422154 131.387077-102.242462l-50.924307-37.572923c-30.562462 48.009846-57.028923 73.058462-80.46277 73.058462-19.377231 0-50.924308-15.675077-94.72-44.898462-44.819692-31.271385-83.534769-45.883077-115.12123-45.883077-44.819692 0-88.615385 33.398154-131.387077 102.242462l50.924307 36.509538c29.538462-47.970462 56.044308-71.995077 80.46277-71.995076 20.361846 0 51.987692 14.611692 96.768 44.898461 43.795692 30.247385 81.526154 45.883077 113.033846 45.883077z",
    },
    grid: {
      left: "0%",
      right: "0%",
      bottom: "5%",
      top: "5%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: valueObj.value.xData,
      axisLabel: {
        fontSize: "16px",
        color: "#fff",
        formatter: function (value) {
          if (value.length > 6) {
            return `${value.slice(0, 6)}...`;
          }
          return value;
        },
      },
    },
    yAxis: {
      type: "value",
      max: 50,
      splitLine: {
        show: true,
        lineStyle: {
          color: "#ffffff70",
          type: "dotted",
        },
      },
      axisLabel: {
        interval: 0,
        fontSize: "14px",
        color: "#fff",
      },
    },
    series: valueObj.value.yData,
  };

  setTimeout(() => {
    myChart?.resize();
  }, 1);

  myChart.setOption(option.value);
  // myChart.getZr().on("click", (params) => {
  //   const pointInPixel = [params.offsetX, params.offsetY];
  //   const chartDom = myChart.getDom();
  //   const chartWidth = chartDom.offsetWidth;
  //   const chartHeight = chartDom.offsetHeight;
  //   const gridOption = option.value.grid;
  //   const left =
  //     typeof gridOption.left === "string" && gridOption.left.endsWith("%")
  //       ? (chartWidth * parseInt(gridOption.left)) / 100
  //       : parseInt(gridOption.left);
  //   const top =
  //     typeof gridOption.top === "string" && gridOption.top.endsWith("%")
  //       ? (chartHeight * parseInt(gridOption.top)) / 100
  //       : parseInt(gridOption.top);
  //   const right =
  //     typeof gridOption.right === "string" && gridOption.right.endsWith("%")
  //       ? (chartWidth * parseInt(gridOption.right)) / 100
  //       : parseInt(gridOption.right);
  //   const bottom =
  //     typeof gridOption.bottom === "string" && gridOption.bottom.endsWith("%")
  //       ? (chartHeight * parseInt(gridOption.bottom)) / 100
  //       : parseInt(gridOption.bottom);
  //   const width = chartWidth - left - right;
  //   const height = chartHeight - top - bottom;
  //   if (
  //     pointInPixel[0] < left ||
  //     pointInPixel[0] > left + width ||
  //     pointInPixel[1] < top ||
  //     pointInPixel[1] > top + height
  //   ) {
  //     return;
  //   }
  //   const xIndex = myChart.convertFromPixel(
  //     { seriesIndex: 0 },
  //     pointInPixel
  //   )[0];
  //   const xAxisData = option.value.xAxis.data;
  //   let xValue = null;
  //   if (xIndex >= 0 && xIndex < xAxisData.length) {
  //     xValue = xAxisData[Math.floor(xIndex)];
  //     const selectedItem = labelList.value.find(
  //       (item) => item.value === currentSelected.value
  //     );
  //     const selectedName = selectedItem ? selectedItem.name : null;
  //     emits("openArticleList", { name: selectedName, time: xValue });
  //   }
  // });
};

// 定义获取数据的函数
const fetchData = async (index) => {
  try {
    // const response = await request.get(`/api/getValueObj?value=${labelList.value[index].value}`);
    // valueObj.value = response.data;
    const data =
      divisionTrend["sccen" + props.sccenId + "type" + currentSelected.value];
    const months = Object.keys(data)
      .sort()
      .map((date) => date);
    const uniqueAiTrendNames = [
      ...new Set(
        Object.values(data)
          .flat()
          .map((item) => item.nsTrendName)
      ),
    ];
    let yData = [];
    let color = [
      "#5B8FF9",
      "#5AD8A6",
      "#5D7092",
      "#F6BD16",
      "#E8684A",
      "#6DC8EC",
      "#9270CA",
      "#FF9D4D",
      "#5B8FF9",
      "#5AD8A6",
      "#5D7092",
      "#F6BD16",
      "#E8684A",
      "#6DC8EC",
      "#9270CA",
      "#FF9D4D",
    ];
    uniqueAiTrendNames.map((rows, key) => {
      yData.push({
        name: rows,
        type: "line",
        data: months.map((month) => {
          const item = data[month].find((item) => item.nsTrendName === rows);
          if (item) {
            return item.nsTrendTotal;
          } else {
            return 0;
          }
        }),
        // connectNulls: true,
        smooth: true,
        itemStyle: {
          color: color[key - 1],
        },
        symbolSize: 8,
        lineStyle: {
          width: 2, // 设置线宽为5
        },
        emphasis: { focus: "series" },
      });
    });
    valueObj.value = {
      xData: months,
      yData: yData,
    };
    if (myChart) {
      option.value.xAxis.data = valueObj.value.xData;
      option.value.series = valueObj.value.yData;
      console.log(option.value);
      myChart.setOption(option.value);
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

const handleSelect = (item) => {
  // currentSelected.value = index;
  // fetchData(index);
  emits("openArticleList", { name: item.name, classifySn: item.value });
};

const getScreenData = () => {
  let color = [
    "#5B8FF9",
    "#5AD8A6",
    "#5D7092",
    "#F6BD16",
    "#E8684A",
    "#6DC8EC",
    "#9270CA",
    "#FF9D4D",
    "#5B8FF9",
    "#5AD8A6",
    "#5D7092",
    "#F6BD16",
    "#E8684A",
    "#6DC8EC",
    "#9270CA",
    "#FF9D4D",
  ];
  wlaqAnalysisList({
    projectSn: "1",
    screenSn: props.sccenId,
    columnSn: "1",
  }).then((res) => {
    let yData = [];
    res.data.map((item, key) => {
      labelList.value.push({
        value: item.classifySn,
        name: item.classify,
      });
      yData.push({
        name: item.classify,
        type: "line",
        data: item.dataMap.map((item) => item.publishCount),
        smooth: true,
        itemStyle: {
          color: color[key],
        },
        symbolSize: 8,
        lineStyle: {
          width: 2, // 设置线宽为5
        },
        emphasis: { focus: "series" },
      });
    });
    valueObj.value.xData = res.data[0].dataMap.map((item) => item.publishTime);
    valueObj.value.yData = yData;
    if (myChart) {
      option.value.xAxis.data = valueObj.value.xData;
      option.value.series = valueObj.value.yData;
      myChart.setOption(option.value);
    }
  });
};

const handleResize = () => {
  myChart?.resize();
};

onMounted(() => {
  nextTick(() => {
    // 确保myChart没有被初始化，防止重复初始化
    if (myChart) {
      myChart.dispose();
    }
    
    let chartDom = document.getElementById("mainLine");
    if (chartDom) {
      myChart = echarts.init(chartDom);
      if (labelList.value.length > 0) {
        currentSelected.value = labelList.value[0].value;
      }
      initChart();
      getScreenData();
      // fetchData(currentSelected.value);
      
      // 添加窗口大小变化的监听
      window.addEventListener('resize', handleResize);
    } else {
      console.error("找不到DOM元素 'mainLine'");
    }
  });
});

onBeforeUnmount(() => {
  // 移除窗口大小变化的监听
  window.removeEventListener('resize', handleResize);
  
  if (myChart) {
    myChart.dispose();
  }
});
</script>

<style lang="scss" scoped>
.tableList {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;

  div {
    width: 148px;
    height: 29px;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    font-size: 16px;
    line-height: 29px;
    color: #05d5ff;
    text-shadow: 0px 1px 2px rgba(5, 213, 255, 0.5);
    background-image: url("@/assets/bigScreen/tableDark.png");
    background-size: 100% 100%;
  }

  .active {
    background-image: url("@/assets/bigScreen/tableBright.png");
    color: white;
  }
}
</style>
