import request from '@/utils/request'

// 查询租户-菜单列表
export function listTenantMenu(query) {
  return request({
    url: '/system/tenantMenu/list',
    method: 'get',
    params: query
  })
}

// 查询租户-菜单详细
export function getTenantMenu(tenantId) {
  return request({
    url: '/system/tenantMenu/' + tenantId,
    method: 'get'
  })
}

// 新增租户-菜单
export function addTenantMenu(data) {
  return request({
    url: '/system/tenantMenu',
    method: 'post',
    data: data
  })
}

// 修改租户-菜单
export function updateTenantMenu(data) {
  return request({
    url: '/system/tenantMenu',
    method: 'put',
    data: data
  })
}

// 删除租户-菜单
export function delTenantMenu(tenantId) {
  return request({
    url: '/system/tenantMenu/' + tenantId,
    method: 'delete'
  })
}
