import request from "@/utils/request";

// 网络安全-网络安全技术信息文章列表
export function wlaqArticleList(query) {
  return request({
    url: "/xty-screen/large/article/list",
    method: "get",
    params: query,
  });
}

// 网络安全-网络安全技术信息文章详情
export function wlaqArticleDetail(query) {
  return request({
    url: "/xty-screen/large/article/detail",
    method: "get",
    params: query,
  });
}

// 风险态势折线图
export function wlaqAnalysisList(query) {
  return request({
    url: "/xty-screen/large/analysis/list",
    method: "post",
    data: query,
  });
}

// 风险态势文章列表
export function analysisArticleList(query) {
  return request({
    url: "/xty-screen/large/analysis/article/list",
    method: "get",
    params: query,
  });
}

// 风险态势文章详情
export function analysisArticleDetail(query) {
  return request({
    url: "/xty-screen/large/analysis/article/detail",
    method: "get",
    params: query,
  });
}

// 关键企业列表
export function enterpriseList(query) {
  return request({
    url: "/xty-screen/large/enterprise/list",
    method: "get",
    params: query,
  });
}

// 关键企业趋势折线图
export function enterpriseTrendData(query) {
  return request({
    url: "/xty-screen/large/enterprise/trend/data",
    method: "get",
    params: query,
  });
}

// 关键企业关联文章列表
export function enterpriseArticleList(query) {
  return request({
    url: "/xty-screen/large/enterprise/article/list",
    method: "get",
    params: query,
  });
}

// 关键企业关联文章详情
export function enterpriseArticleDetail(query) {
  return request({
    url: "/xty-screen/large/enterprise/article/detail",
    method: "get",
    params: query,
  });
}

// 关键人列表
export function expertsList(query) {
  return request({
    url: "/xty-screen/large/experts/list",
    method: "get",
    params: query,
  });
}

// 关键人趋势折线图
export function expertsTrendData(query) {
  return request({
    url: "/xty-screen/large/experts/trend/data",
    method: "get",
    params: query,
  });
}

// 关键人关联文章列表
export function expertsArticleList(query) {
  return request({
    url: "/xty-screen/large/experts/article/list",
    method: "get",
    params: query,
  });
}

// 关键人关联文章详情
export function expertsArticleDetail(query) {
  return request({
    url: "/xty-screen/large/experts/article/detail",
    method: "get",
    params: query,
  });
}

// 专家观点列表
export function viewpointList(query) {
  return request({
    url: "/xty-screen/large/viewpoint/list",
    method: "get",
    params: query,
  });
}

// 专家观点详情
export function viewpointDetail(query) {
  return request({
    url: "/xty-screen/large/viewpoint/detail",
    method: "get",
    params: query,
  });
}

// 网络安全-重点事件脉络列表
export function eventsList(query) {
  return request({
    url: "/xty-screen/large/events/list",
    method: "get",
    params: query,
  });
}

// 网络安全-重点事件时间轴
export function eventsNodesData(query) {
  return request({
    url: "/xty-screen/large/events/nodes/data",
    method: "get",
    params: query,
  });
}

// 网络安全-重点事件脉络分析
export function eventsAnalysisData(query) {
  return request({
    url: "/xty-screen/large/events/analysis/data",
    method: "get",
    params: query,
  });
}

// 打压风险时间轴
export function suppressData(query) {
  return request({
    url: "/xty-screen/large/suppress/data",
    method: "post",
    data: query,
  });
}

// 打压风险情况
export function suppressLevelCount(query) {
  return request({
    url: "/xty-screen/large/suppress/level/count",
    method: "post",
    data: query,
  });
}

// 打压风险企业列表
export function suppressEnterpriseList(query) {
  return request({
    url: "/xty-screen/large/suppress/list",
    method: "get",
    params: query,
  });
}

// 打压风险企业详情专利专利
export function suppressPatentList(query) {
  return request({
    url: "/xty-screen/large/suppress/patent/list",
    method: "get",
    params: query,
  });
}

// 打压风险企业详情软著
export function suppressSoftwareList(query) {
  return request({
    url: "/xty-screen/large/suppress/software/list",
    method: "get",
    params: query,
  });
}

// 人工智能-技术架构
export function aiData(query) {
  return request({
    url: "/xty-screen/large/ai/data",
    method: "post",
    data: query,
  });
}

// 人工智能-技术架构文章列表
export function aiArticleList(query) {
  return request({
    url: "/xty-screen/large/ai/article/list",
    method: "get",
    params: query,
  });
}

// 人工智能-技术架构文章详情
export function aiArticleDetail(query) {
  return request({
    url: "/xty-screen/large/ai/article/detail",
    method: "get",
    params: query,
  });
}

// 人工智能-政策分析地图
export function proposalsCount(query) {
  return request({
    url: "/xty-screen/large/proposals/count",
    method: "post",
    data: query,
  });
}

// 人工智能-政策分析提案列表
export function proposalsList(query) {
  return request({
    url: "/xty-screen/large/proposals/list",
    method: "get",
    params: query,
  });
}

// 人工智能-政策分析提案与中国是否有关
export function proposalsToChinaData(query) {
  return request({
    url: "/xty-screen/large/proposals/toChina/data",
    method: "post",
    data: query,
  });
}

// 人工智能-政策分析提案详情
export function proposalsDetail(query) {
  return request({
    url: "/xty-screen/large/proposals/detail",
    method: "get",
    params: query,
  });
}

// 人工智能-政策分析提案人员详情
export function proposalsExpertDetail(query) {
  return request({
    url: "/xty-screen/large/proposals/expert/detail",
    method: "get",
    params: query,
  });
}

// 前沿技术热点词
export function technicalData(data) {
  return request({
    url: "/xty-screen/large/technical/data",
    method: "post",
    data: data,
  });
}

// 前沿技术趋势图数据
export function technicalTrendData(data) {
  return request({
    url: "/xty-screen/large/technical/trend/data",
    method: "get",
    params: data,
  });
}

// 前沿技术文章列表
export function technicalArticleList(data) {
  return request({
    url: "/xty-screen/large/technical/article/list",
    method: "get",
    params: data,
  });
}

// 前沿技术文章详情
export function technicalArticleDetail(data) {
  return request({
    url: "/xty-screen/large/technical/article/detail",
    method: "get",
    params: data,
  });
}

// 查询技术报告列表
export function technicalReportList(data) {
  return request({
    url: "/xty-screen/large/technical/report/list",
    method: "get",
    params: data,
  });
}

// 前沿技术3d图数据
export function technical3dData(data) {
  return request({
    url: "/xty-screen/large/technical/3d/data",
    method: "post",
    data: data,
  });
}

// 前沿技术报告
export function technicalReportData(data) {
  return request({
    url: "/xty-screen/large/technical/report/data",
    method: "post",
    data: data,
  });
}
// 前沿技术报告详情
export function technicalReportDetail(data) {
  return request({
    url: "/xty-screen/large/technical/report/detail",
    method: "get",
    params: data,
  });
}