import request from '@/utils/request'

// 查询热点事件列表
export function listEvents(query) {
  return request({
    url: '/screen/events/list',
    method: 'get',
    params: query
  })
}

// 查询热点事件详细
export function getEvents(id) {
  return request({
    url: '/screen/events/' + id,
    method: 'get'
  })
}

// 新增热点事件
export function addEvents(data) {
  return request({
    url: '/screen/events',
    method: 'post',
    data: data
  })
}

// 修改热点事件
export function updateEvents(data) {
  return request({
    url: '/screen/events/edit',
    method: 'post',
    data: data
  })
}

// 删除热点事件
export function delEvents(data) {
  return request({
    url: '/screen/events/remove',
    method: 'post',
    data: data
  })
}
