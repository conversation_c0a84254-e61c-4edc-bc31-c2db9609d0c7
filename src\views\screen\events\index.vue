<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="事件编码" prop="eventsSn">
        <el-input v-model="queryParams.eventsSn" placeholder="请输入事件编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectSn">
        <el-input v-model="queryParams.projectSn" placeholder="请输入项目编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="屏幕编码" prop="screenSn">
        <el-input v-model="queryParams.screenSn" placeholder="请输入屏幕编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="栏目编码" prop="columnSn">
        <el-input v-model="queryParams.columnSn" placeholder="请输入栏目编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="租户编码" prop="tenantSn">
        <el-input v-model="queryParams.tenantSn" placeholder="请输入租户编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['screen:events:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:events:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:events:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['screen:events:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          v-hasPermi="['screen:events:add']">重点事件导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImportNodes"
          v-hasPermi="['screen:eventsNodes:add']">事件节点导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImportAnalysis"
          v-hasPermi="['screen:eventsAnalysis:add']">相关内容导入</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="eventsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="事件编码" width="150" prop="eventsSn" />
      <el-table-column label="事件标题" min-width="300" show-overflow-tooltip prop="eventsTitle" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目编码" align="center" prop="projectSn" />
      <el-table-column label="屏幕编码" align="center" prop="screenSn" />
      <el-table-column label="栏目编码" align="center" prop="columnSn" />
      <el-table-column label="租户编码" align="center" prop="tenantSn" show-overflow-tooltip/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="340">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:events:edit']">修改</el-button>
          <el-button link type="primary" icon="Edit" @click="handleNodes(scope.row)">事件节点
          </el-button>
          <el-button link type="primary" icon="Edit" @click="handleAnalysis(scope.row)">相关内容
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:events:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改热点事件对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="eventsRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="唯一标识" prop="eventsSn">
          <el-input v-model="form.eventsSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="事件标题" prop="eventsTitle">
          <el-input v-model="form.eventsTitle" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-row>
          <el-col :span="12" v-if="userStore.roles.includes('admin')">
            <el-form-item label="租户编码" prop="tenantSn">
              <el-input v-model="form.tenantSn" placeholder="请输入租户编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="projectSn">
              <el-input v-model="form.projectSn" placeholder="请输入项目编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕编码" prop="screenSn">
              <el-input v-model="form.screenSn" placeholder="请输入屏幕编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="栏目编码" prop="columnSn">
              <el-input v-model="form.columnSn" placeholder="请输入栏目编码" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <eventsAnalysis v-model:visible="eventsAnalysisVisible" :sn="eventsAnalysisSn" :visibleTitle="eventsAnalysisTitle"
      @closeAnalysis="closeAnalysis">
    </eventsAnalysis>
    <eventsNodes v-model:visible="eventsNodesVisible" :sn="eventsNodesSn" :visibleTitle="eventsNodesTitle"
      @closeNodes="closeNodes">
    </eventsNodes>
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Events">
import { getToken } from "@/utils/auth";
import { listEvents, getEvents, delEvents, addEvents, updateEvents } from "@/api/screen/events";
import eventsAnalysis from "../secondLevel/eventsAnalysis/index.vue";
import eventsNodes from "../secondLevel/eventsNodes/index.vue";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const eventsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    eventsSn: null,
    eventsTitle: null,
    keywords: null,
    summary: null,
    cover: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    projectSn: null,
    screenSn: null,
    columnSn: null,
    tenantSn: null
  },
  rules: {
    eventsTitle: [
      { required: true, message: "事件标题不能为空", trigger: "blur" }
    ],
  },
  eventsAnalysisVisible: false,
  eventsAnalysisSn: null,
  eventsAnalysisTitle: '',
  eventsNodesVisible: false,
  eventsNodesSn: null,
  eventsNodesTitle: '',
});

const { queryParams, form, rules, eventsAnalysisVisible, eventsAnalysisSn, eventsAnalysisTitle, eventsNodesVisible, eventsNodesSn, eventsNodesTitle, } = toRefs(data);

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  businessType: null,
  headers: { Authorization: "Bearer " + getToken() },
  url: ''
});

/** 查询热点事件列表 */
function getList() {
  loading.value = true;
  listEvents(queryParams.value).then(response => {
    eventsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    eventsSn: null,
    eventsTitle: null,
    keywords: null,
    summary: null,
    cover: null,
    status: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    delFlag: null,
    deleteBy: null,
    deleteTime: null,
    projectSn: null,
    screenSn: null,
    columnSn: null,
    tenantSn: null
  };
  proxy.resetForm("eventsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加热点事件";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getEvents(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改热点事件";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["eventsRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateEvents(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addEvents(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  _ids = _ids.flatMap(item => {
    const found = eventsList.value.find(row => row.id === item)
    return found?.eventsSn ? [found.eventsSn] : []
  })
  proxy.$modal.confirm('是否确认删除热点事件编号为"' + _ids + '"的数据项？').then(function () {
    return delEvents(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/events/export', {
    ...queryParams.value
  }, `events_${new Date().getTime()}.xlsx`)
}

const handleAnalysis = (row) => {
  eventsAnalysisSn.value = row.eventsSn
  eventsAnalysisTitle.value = row.eventsTitle
  eventsAnalysisVisible.value = true
}

const closeAnalysis = () => {
  eventsAnalysisVisible.value = false
}

const handleNodes = (row) => {
  eventsNodesSn.value = row.eventsSn
  eventsNodesTitle.value = row.eventsTitle
  eventsNodesVisible.value = true
}

const closeNodes = () => {
  eventsNodesVisible.value = false
}

function handleImport() {
  upload.title = "重点事件导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/events/excelImport";
  upload.businessType = 100;
  upload.open = true;
};

function handleImportNodes() {
  upload.title = "事件节点导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/eventsNodes/excelImport";
  upload.businessType = 101;
  upload.open = true;
};

function handleImportAnalysis() {
  upload.title = "相关内容导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/eventsAnalysis/excelImport";
  upload.businessType = 102;
  upload.open = true;
};

function importTemplate() {
  proxy.download("xty-screen/events/downloadTemplate?businessType=" + upload.businessType, {
  }, `${upload.title}模版.xlsx`);
};

const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};

function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

getList();
</script>
