<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
      class="queryForm"
    >
      <el-row>
        <el-col :span="5">
          <el-form-item label="专家观点编码" prop="viewpointSn">
            <el-input
              v-model="queryParams.viewpointSn"
              placeholder="请输入专家观点编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="专家姓名" prop="expertsName">
            <el-input
              v-model="queryParams.expertsName"
              placeholder="请输入专家姓名"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="发布时间" prop="publishTime">
            <el-date-picker
              clearable
              v-model="queryParams.publishTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择发布时间"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="租户编码" prop="tenantSn">
            <el-input
              v-model="queryParams.tenantSn"
              placeholder="请输入租户编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="20"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="项目编码" prop="projectSn">
            <el-input
              v-model="queryParams.projectSn"
              placeholder="请输入项目编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="20"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="屏幕编码" prop="screenSn">
            <el-input
              v-model="queryParams.screenSn"
              placeholder="请输入屏幕编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="20"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="栏目编码" prop="columnSn">
            <el-input
              v-model="queryParams.columnSn"
              placeholder="请输入栏目编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="20"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item style="margin-left: 20px">
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['screen:viewpoint:add']"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:viewpoint:edit']"
          >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:viewpoint:remove']"
          >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['screen:viewpoint:export']"
          >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['screen:viewpoint:add']"
          >专家观点导入</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="viewpointList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 320px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="专家观点编码"
        align="center"
        prop="viewpointSn"
        width="110"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.viewpointSn"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.viewpointSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="专家观点"
        align="center"
        prop="viewpoint"
        width="300"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.viewpoint"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.viewpoint }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="专家姓名"
        align="center"
        prop="expertsName"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.expertsName"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.expertsName }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="文章原文链接URL"
        align="center"
        prop="originalUrl"
        width="150"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.originalUrl"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.originalUrl }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="关联站内文章"
        align="center"
        prop="sn"
        width="150"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.sn"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.sn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="数据源sn"
        align="center"
        prop="sourceSn"
        width="150"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.sourceSn"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.sourceSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="关联文章来源"
        align="center"
        prop="sourceName"
        width="110"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.sourceName"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.sourceName }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="关联文章标题"
        align="center"
        prop="title"
        width="300"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.title"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.title }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="文章摘要"
        align="center"
        prop="smmary"
        width="300"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.smmary"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.smmary }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="关联文章内容"
        align="center"
        prop="content"
        width="300"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.content"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.content }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="文章内容英文"
        align="center"
        prop="enContent"
        width="300"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.enContent"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.enContent }}</div>
          </el-tooltip>
        </template>
      </el-table-column> -->
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishTime"
        width="110"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="关键词"
        align="center"
        prop="keywords"
        width="200"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.keywords"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.keywords }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === "0" ? "正常" : "停用"
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="租户编码"
        align="center"
        prop="tenantSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.tenantSn"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.tenantSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="项目编码"
        align="center"
        prop="projectSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.projectSn"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.projectSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="屏幕编码"
        align="center"
        prop="screenSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.screenSn"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.screenSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="栏目编码"
        align="center"
        prop="columnSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.columnSn"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.columnSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" width="150">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.remark"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.remark }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <div class="btns">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['screen:viewpoint:edit']"
              >修改
            </el-button>
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['screen:viewpoint:remove']"
              >删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改专家观点对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form
        ref="viewpointRef"
        :model="form"
        :rules="rules"
        label-width="125px"
      >
        <el-form-item
          label="专家观点编码"
          prop="viewpointSn"
          v-if="title === '修改专家观点'"
        >
          <el-input
            v-model="form.viewpointSn"
            placeholder="请输入专家观点编码"
            disabled
          />
        </el-form-item>
        <el-form-item label="专家观点" prop="viewpoint">
          <editor v-model="form.viewpoint" :min-height="192" :key="editorKey" />
        </el-form-item>
        <el-form-item label="专家姓名" prop="expertsName">
          <el-input
            v-model="form.expertsName"
            placeholder="请输入专家姓名"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          label="关联ID"
          prop="expertsSn"
          v-if="title === '修改专家观点'"
        >
          <el-input
            v-model="form.expertsSn"
            placeholder="请输入关联ID"
            disabled
          />
        </el-form-item>
        <el-form-item label="文章原文链接URL" prop="originalUrl">
          <el-input
            v-model="form.originalUrl"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="300"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="关联站内文章" prop="sn">
          <el-input
            v-model="form.sn"
            placeholder="请输入关联站内文章"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="数据源sn" prop="sourceSn">
          <el-input
            v-model="form.sourceSn"
            placeholder="请输入数据源sn"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="关联文章来源" prop="sourceName">
          <el-input
            v-model="form.sourceName"
            placeholder="请输入关联文章来源"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="关联文章标题" prop="title">
          <el-input
            v-model="form.title"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="文章摘要" prop="smmary">
          <editor v-model="form.smmary" :min-height="192" :key="editorKey" />
        </el-form-item>
        <el-form-item label="关联文章内容">
          <editor v-model="form.content" :min-height="192" :key="editorKey" />
        </el-form-item>
        <el-form-item label="文章内容英文">
          <editor v-model="form.enContent" :min-height="192" :key="editorKey" />
        </el-form-item>
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker
            clearable
            v-model="form.publishTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择发布时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input
            v-model="form.keywords"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12" v-if="userStore.roles.includes('admin')">
            <el-form-item label="租户编码" prop="tenantSn">
              <el-input
                v-model="form.tenantSn"
                placeholder="请输入租户编码"
                :maxlength="20"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="projectSn">
              <el-input
                v-model="form.projectSn"
                placeholder="请输入项目编码"
                :maxlength="20"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕编码" prop="screenSn">
              <el-input
                v-model="form.screenSn"
                placeholder="请输入屏幕编码"
                :maxlength="20"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="栏目编码" prop="columnSn">
              <el-input
                v-model="form.columnSn"
                placeholder="请输入栏目编码"
                :maxlength="20"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Viewpoint">
import { getToken } from "@/utils/auth";
import {
  addViewpoint,
  delViewpoint,
  getViewpoint,
  listViewpoint,
  updateViewpoint,
} from "@/api/screen/viewpoint";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const viewpointList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const editorKey = ref(0);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    viewpointSn: null,
    viewpoint: null,
    expertsName: null,
    expertsSn: null,
    originalUrl: null,
    sn: null,
    sourceSn: null,
    sourceName: null,
    title: null,
    smmary: null,
    content: null,
    enContent: null,
    publishTime: null,
    keywords: null,
    status: null,
    deleteTime: null,
    deleteBy: null,
    tenantSn: null,
    projectSn: null,
    screenSn: null,
    columnSn: null,
  },
  rules: {
    // viewpointSn: [{ required: true, message: "专家观点编码不能为空", trigger: "blur" }],
    viewpoint: [
      { required: true, message: "专家观点不能为空", trigger: "blur" },
    ],
    expertsName: [
      // { required: true, message: "专家姓名不能为空", trigger: "blur" },
      { max: 50, message: "专家姓名长度不能超过50个字符", trigger: "blur" },
    ],
    // expertsSn: [
    //   { required: true, message: "关联ID不能为空", trigger: "blur" },
    //   { max: 50, message: "关联ID长度不能超过50个字符", trigger: "blur" },
    // ],
    originalUrl: [
      {
        max: 300,
        message: "文章原文链接URL长度不能超过300个字符",
        trigger: "blur",
      },
    ],
    sn: [
      { max: 50, message: "关联站内文章长度不能超过50个字符", trigger: "blur" },
    ],
    sourceSn: [
      { max: 50, message: "数据源sn长度不能超过50个字符", trigger: "blur" },
    ],
    sourceName: [
      { max: 50, message: "关联文章来源长度不能超过50个字符", trigger: "blur" },
    ],
    title: [
      {
        max: 100,
        message: "关联文章标题长度不能超过100个字符",
        trigger: "blur",
      },
    ],
    keywords: [
      { max: 100, message: "关键词长度不能超过100个字符", trigger: "blur" },
    ],
    tenantSn: [
      { max: 20, message: "租户编码长度不能超过20个字符", trigger: "blur" },
    ],
    projectSn: [
      { max: 20, message: "项目编码长度不能超过20个字符", trigger: "blur" },
    ],
    screenSn: [
      { max: 20, message: "屏幕编码长度不能超过20个字符", trigger: "blur" },
    ],
    columnSn: [
      { max: 20, message: "栏目编码长度不能超过20个字符", trigger: "blur" },
    ],
    remark: [
      { max: 100, message: "备注长度不能超过100个字符", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  businessType: null,
  headers: { Authorization: "Bearer " + getToken() },
  url: "",
});

/** 查询专家观点列表 */
function getList() {
  loading.value = true;
  listViewpoint(queryParams.value).then((response) => {
    viewpointList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    viewpoint: null,
    expertsName: null,
    originalUrl: null,
    sn: null,
    sourceSn: null,
    sourceName: null,
    title: null,
    smmary: null,
    content: null,
    enContent: null,
    publishTime: null,
    keywords: null,
    projectSn: null,
    screenSn: null,
    columnSn: null,
    remark: null,
  };
  if (userStore.roles.includes("admin")) {
    form.value.tenantSn = null;
  }
  proxy.resetForm("viewpointRef");
  editorKey.value += 1;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.viewpointSn);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加专家观点";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getViewpoint(_id).then((response) => {
    const responseData = response.data;
    const resetFields = [
      "viewpointSn",
      "viewpoint",
      "expertsName",
      "expertsSn",
      "originalUrl",
      "sn",
      "sourceSn",
      "sourceName",
      "title",
      "smmary",
      "content",
      "enContent",
      "publishTime",
      "keywords",
      "projectSn",
      "screenSn",
      "columnSn",
      "remark",
    ];
    form.value.id = responseData.id;
    resetFields.forEach((field) => {
      form.value[field] = responseData[field] || null;
    });
    if (userStore.roles.includes("admin")) {
      form.value.tenantSn = responseData.tenantSn;
    }
    open.value = true;
    title.value = "修改专家观点";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["viewpointRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateViewpoint(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addViewpoint(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.viewpointSn || ids.value;
  proxy.$modal
    .confirm('是否确认删除专家观点编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delViewpoint(Array.isArray(_ids) ? _ids : [_ids]);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "screen/viewpoint/export",
    {
      ...queryParams.value,
    },
    `viewpoint_${new Date().getTime()}.xlsx`
  );
}

/** 专家观点导入 */
function handleImport() {
  upload.title = "专家观点导入";
  upload.url =
    import.meta.env.VITE_APP_BASE_API + "/xty-screen/viewpoint/excelImport";
  upload.businessType = 100;
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download(
    "xty-screen/viewpoint/downloadTemplate",
    {},
    `${upload.title}模版.xlsx`
  );
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

getList();
</script>

<style scoped lang="scss">
:deep(.queryForm) {
  .el-col-5 {
    max-width: 20%;
    flex: 0 0 20%;
  }
  .el-form-item {
    width: 100%;
    margin-right: 0;
  }
  .el-form-item__content {
    width: calc(100% - 100px);
  }
}
.cell-content {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 1.5;
}

.btns {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.btns .el-button {
  margin-left: 0;
}

.dialog-pagination .el-pagination {
  right: 20px !important;
}
</style>
