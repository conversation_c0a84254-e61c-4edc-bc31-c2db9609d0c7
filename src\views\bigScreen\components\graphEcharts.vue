<template>
  <div id="mainGraph" style="width: 100%; height: 100%; padding: 10px 20px"></div>
</template>
  
<script setup>
import * as echarts from "echarts";
import { technicalData } from "@/api/bigScreen/index";

const emits = defineEmits(["openTechnologyDetails"]);
const props = defineProps({
  sccenId: {
    type: Number,
    default: 1,
  },
});

var myChart = null;
const option = ref({});
const color = ['#E26B72FF', '#C1B973FF', '#D56149FF', '#4CB5F6FF', '#4C85F6FF', '#66C9D5FF', '#73C193FF', '#C1B973FF', '#AE66AEFF', '#E26B72FF', '#C1B973FF', '#D56149FF', '#4CB5F6FF', '#4C85F6FF', '#66C9D5FF', '#73C193FF', '#C1B973FF', '#AE66AEFF', '#E26B72FF', '#C1B973FF', '#D56149FF', '#4CB5F6FF', '#4C85F6FF', '#66C9D5FF', '#73C193FF', '#C1B973FF', '#AE66AEFF'];

const initChart = () => {
  technicalData({ "projectSn": "1", "screenSn": props.sccenId, "columnSn": "1" }).then(res => {
    const data = res.data.map((item, index) => {
      return {
        id: item.technicalSn,
        name: item.technicalName,
        value: item.totalArticles,
        summary: item.summary,
        symbolSize: Math.max(50, Math.min(item.totalArticles / 25, 80)),
        itemStyle: {
          color: item.backgroundColor ? item.backgroundColor : color[index]
        },
        label: {
          color: item.fontColor ? item.fontColor : '#fff',
        },
      }
    })
    option.value = {
      tooltip: {
        show: true,
      },
      grid: {
        left: "0%",
        right: "0%",
        bottom: "0%",
        top: "0%",
        containLabel: true,
      },
      series: [
        {
          type: 'graph',
          layout: 'force',
          force: {
            repulsion: 110,
            edgeLength: [0, 400],
            gravity: 0.1
          },
          symbolSize: 50,
          roam: true,
          label: {
            show: true,
          },
          edgeSymbol: ['circle', 'arrow'],
          edgeSymbolSize: [4, 10],
          edgeLabel: {
            fontSize: 20
          },
          data: data,
        }
      ]
    }
    myChart.setOption(option.value);
    myChart.on('click', (params) => {
      emits("openTechnologyDetails", { ...params, });
    });
    setTimeout(() => {
      myChart?.resize();
    }, 1);
  })
};

onMounted(() => {
  let chartDom = document.getElementById("mainGraph");
  myChart = echarts.init(chartDom);
  initChart();
});

onBeforeUnmount(() => {
  if (myChart) {
    myChart.dispose();
  }
});
</script>
  
<style lang="scss" scoped></style>
  