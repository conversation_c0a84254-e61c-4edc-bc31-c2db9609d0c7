<template>
  <div
    v-if="visible"
    class="custom-dialog-mask"
    @click="handleMaskClick"
    :style="{ zIndex: currentZIndex }"
  >
    <div
      class="custom-dialog"
      :style="{ width: props.width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ tianDetail?.proposalsTitle }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">提案摘要</div>
          <div class="bg-box-content" v-html="tianDetail.summary"></div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">提案详情</div>
          <div class="toggle-divs">
            <div
              @click="activeContent = 'proposalsEnContent'"
              :class="{ active: activeContent === 'proposalsEnContent' }"
            >
              原文
            </div>
            <div
              @click="activeContent = 'proposalsContent'"
              :class="{ active: activeContent === 'proposalsContent' }"
            >
              中文
            </div>
          </div>
          <div class="bg-box-content" v-html="tianDetail[activeContent]"></div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">提案人员</div>
          <div class="bg-box-content">
            <el-table :data="tianDetail.experts" border style="width: 100%">
              <el-table-column
                prop="proposalsExperts"
                align="center"
                label="提案人员"
              >
                <template #default="scope">
                  <div
                    style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                    @click="openExpertsContent(scope.row.expertsSn)"
                  >
                    {{ scope.row.proposalsExperts }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="belongToGroup"
                align="center"
                label="所属党派"
              />
              <el-table-column
                prop="belongToArea"
                align="center"
                label="所属州"
              />
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch, computed } from "vue";
import { getNewZIndex, getBaseZIndex } from "@/utils/zIndexManager";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1000,
  },
  tianDetail: {
    type: Object,
    default: () => {},
  },
});

// 当前对话框的 z-index
const currentZIndex = ref(getBaseZIndex());

const emits = defineEmits(["update:visible", "openExpertsContent"]);

// 提升组件到最上层的方法
const bringToFront = () => {
  currentZIndex.value = getNewZIndex();
};

// 对外暴露方法
defineExpose({
  bringToFront,
});

watch(
  () => props.visible,
  (value) => {
    activeContent.value = "proposalsContent";
    // 当对话框变为可见时，更新 z-index
    if (value) {
      bringToFront();
    }
  }
);

const closeDialog = () => {
  emits("update:visible", false);
};

const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};

// 定义一个响应式变量来记录当前选中的内容类型
const activeContent = ref("proposalsContent");

const openExpertsContent = (id) => {
  emits("openExpertsContent", id);
};

// 第二种方案（暂时注释掉）
// const formattedSummary = computed(() => {
//   return props.tianDetail.summary ? props.tianDetail.summary.replace(/\n/g, '<br>') : '';
// });

// const formattedContent = computed(() => {
//   return props.tianDetail[activeContent.value] ? props.tianDetail[activeContent.value].replace(/\n/g, '<br>') : '';
// });
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  // z-index: 1000; // 移除固定 z-index，使用动态值

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 40px;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        position: relative;
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .toggle-divs {
          position: absolute;
          display: flex;
          top: 16px;
          right: 16px;

          div {
            width: 48px;
            height: 30px;
            background-color: #3a4d68ff;
            color: white;
            padding: 5px 10px;
            cursor: pointer;
            user-select: none; // 禁止文本选中

            &:hover {
              background-color: #1d3046;
            }

            // 选中状态的高亮样式
            &.active {
              background-color: #ff9d00ff;
              color: white;
            }
          }
        }

        .bg-box-content {
          // display: flex;
          // justify-content: center;
          font-size: 16px;
          color: #ffffff;
          white-space: pre-wrap;

          :deep(.el-table__header th) {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          :deep(.el-table__body td) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }

          :deep(.el-descriptions__label) {
            background-color: #1f3850;
            color: rgba(255, 255, 255);
            font-size: 16px;
            text-align: center;
          }

          :deep(.el-descriptions__content) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
