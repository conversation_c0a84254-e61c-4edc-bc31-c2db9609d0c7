import request from '@/utils/request'

// 查询关键企业文章列表
export function listEnterpriseArticle(query) {
  return request({
    url: '/screen/enterpriseArticle/list',
    method: 'get',
    params: query
  })
}

// 查询关键企业文章详细
export function getEnterpriseArticle(id) {
  return request({
    url: '/screen/enterpriseArticle/' + id,
    method: 'get'
  })
}

// 新增关键企业文章
export function addEnterpriseArticle(data) {
  return request({
    url: '/screen/enterpriseArticle',
    method: 'post',
    data: data
  })
}

// 修改关键企业文章
export function updateEnterpriseArticle(data) {
  return request({
    url: '/screen/enterpriseArticle',
    method: 'put',
    data: data
  })
}

// 删除关键企业文章
export function delEnterpriseArticle(id) {
  return request({
    url: '/screen/enterpriseArticle/' + id,
    method: 'delete'
  })
}
