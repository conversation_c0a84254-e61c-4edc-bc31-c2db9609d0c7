import request from '@/utils/request'

// 查询人工智能架构列表
export function listAi(query) {
  return request({
    url: '/screen/ai/list',
    method: 'get',
    params: query
  })
}

// 查询人工智能架构详细
export function getAi(id) {
  return request({
    url: '/screen/ai/' + id,
    method: 'get'
  })
}

// 新增人工智能架构
export function addAi(data) {
  return request({
    url: '/screen/ai',
    method: 'post',
    data: data
  })
}

// 修改人工智能架构
export function updateAi(data) {
  return request({
    url: '/screen/ai/edit',
    method: 'post',
    data: data
  })
}

// 删除人工智能架构
export function delAi(data) {
  return request({
    url: '/screen/ai/remove',
    method: 'post',
    data: data
  })
}
