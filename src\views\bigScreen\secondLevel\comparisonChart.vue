<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div v-show="show" class="custom-dialog" :style="{ width: props.width + 'px' }" @click.stop>
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box" v-loading="loading">
          <div class="bg-box-title">国内技术报告</div>
          <div class="bg-box-content">
            <el-table :data="reportData" style="width: calc(100% - 20px);margin: 0 10px;background-color: #00000000;">
              <el-table-column prop="reportSn" label="序号" width="60" align="center">
                <template #default="scope">
                  {{ (scope.$index + 1) + ((queryParams.pageNum - 1) * queryParams.pageSize) }}
                </template>
              </el-table-column>
              <el-table-column prop="publishTime" label="发布时间" width="110" align="center" />
              <el-table-column prop="publishUnit" label="发布机构" width="240" show-overflow-tooltip />
              <el-table-column prop="reportName" label="报告名称" show-overflow-tooltip>
                <template #default="scope">
                  <div style="color: #0ec2f4ff; cursor: pointer" @click="
                    openHotTechnologyDetails({ ...scope.row, title: scope.row.reportName })
                  ">
                    {{ scope.row.reportName }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize" @pagination="getList" />
          </div>
        </div>
        <div class="bg-box" v-loading="loading1">
          <div class="bg-box-title">国外技术报告</div>
          <div class="bg-box-content">
            <el-table :data="reportData1" style="width: calc(100% - 20px);margin: 0 10px;background-color: #00000000;">
              <el-table-column prop="reportSn" label="序号" width="60" align="center">
                <template #default="scope">
                  {{ (scope.$index + 1) + ((queryParams1.pageNum - 1) * queryParams1.pageSize) }}
                </template>
              </el-table-column>
              <el-table-column prop="publishTime" label="发布时间" width="110" align="center" />
              <el-table-column prop="publishUnit" label="发布机构" width="240" show-overflow-tooltip />
              <el-table-column prop="reportName" label="报告名称" show-overflow-tooltip>
                <template #default="scope">
                  <div style="color: #0ec2f4ff; cursor: pointer" @click="
                    openHotTechnologyDetails({ ...scope.row, title: scope.row.reportName })
                  ">
                    {{ scope.row.reportName }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="total1 > 0" :total="total1" v-model:page="queryParams1.pageNum"
              v-model:limit="queryParams1.pageSize" @pagination="getList1" />
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">国内热点技术</div>
          <div class="bg-box-content content-flex">
            <baar3DEcharts :show="show" :sccenId="sccenId" :type="1" style="width: 1200px; height: 500px"></baar3DEcharts>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">国外热点技术</div>
          <div class="bg-box-content content-flex">
            <baar3DEcharts :show="show" :sccenId="sccenId" :type="0" style="width: 1200px; height: 500px"></baar3DEcharts>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed, watch } from "vue";
import baar3DEcharts from "../components/baar3DEcharts";
import { technicalReportList } from "@/api/bigScreen/index";

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "自定义弹窗",
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1280,
  },
  sccenId: {
    type: Number,
    default: 1,
  },
});
const emits = defineEmits(["openHotTechnology", "update:visible"]);

const data = reactive({
  reportData: [{ name: '1' }],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  total: 0,
  reportData1: [{ name: '1' }],
  queryParams1: {
    pageNum: 1,
    pageSize: 10,
  },
  total1: 0,
  show: false,
  loading: true,
  loading1: true,
})
const { reportData, reportData1, queryParams, queryParams1, total, total1, show, loading, loading1 } = toRefs(data)

watch(() => props.visible, (newVal) => {
  if (newVal) {
    data.queryParams = {
      pageNum: 1,
      pageSize: 10,
    }
    data.queryParams1 = {
      pageNum: 1,
      pageSize: 10,
    }
    data.show = false
    getList()
    getList1()
  }
})


const getList = () => {
  data.loading = true
  technicalReportList({ ...queryParams.value, projectSn: '1', screenSn: props.sccenId, columnSn: '1', isForeign: 1 }).then(res => {
    reportData.value = res.rows
    total.value = res.total
    data.show = true
    data.loading = false
  })
}

const getList1 = () => {
  data.loading1 = true
  technicalReportList({ ...queryParams1.value, projectSn: '1', screenSn: props.sccenId, columnSn: '1', isForeign: 0 }).then(res => {
    reportData1.value = res.rows
    total1.value = res.total
    data.show = true
    data.loading1 = false
  })
}

const openHotTechnologyDetails = (data) => {
  emits("openHotTechnology", data);
};

// 关闭弹窗的方法
const closeDialog = () => {
  emits("update:visible", false);
};

// 处理遮罩层点击事件
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          font-size: 16px;

          .bg-box-content-list {
            padding: 0 0 10px 15px;
            font-size: 14px;
            color: #ffffffc6;

            span {
              font-weight: 600;
              color: #ffffff;
              margin-right: 10px;
            }
          }

          :deep(.el-table tr) {
            background-color: #1f3850ff !important;
          }

          :deep(.el-table__inner-wrapper:before) {
            height: 0px;
            border-color: #00000000;
          }

          :deep(.el-table__header th) {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
            border-bottom-width: 0px;
          }

          :deep(.el-table__body td) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            border-bottom-width: 0px;
          }

          :deep(.el-table__body tr:hover > td) {
            background-color: #132f56 !important;
          }
        }

        .content-flex {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}

:deep(.pagination-container) {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump) {
  color: #f2f2f2;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination button:disabled) {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

:deep(.el-pager li) {
  background: #ffffff00 !important;
  color: #fff !important;

  &.is-active {
    color: #1890ff !important;
  }
}
</style>
