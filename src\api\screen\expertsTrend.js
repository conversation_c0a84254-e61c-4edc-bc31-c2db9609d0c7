import request from '@/utils/request'

// 查询关键人趋势数据列表
export function listExpertsTrend(query) {
  return request({
    url: '/screen/expertsTrend/list',
    method: 'get',
    params: query
  })
}

// 查询关键人趋势数据详细
export function getExpertsTrend(id) {
  return request({
    url: '/screen/expertsTrend/' + id,
    method: 'get'
  })
}

// 新增关键人趋势数据
export function addExpertsTrend(data) {
  return request({
    url: '/screen/expertsTrend',
    method: 'post',
    data: data
  })
}

// 修改关键人趋势数据
export function updateExpertsTrend(data) {
  return request({
    url: '/screen/expertsTrend',
    method: 'put',
    data: data
  })
}

// 删除关键人趋势数据
export function delExpertsTrend(id) {
  return request({
    url: '/screen/expertsTrend/' + id,
    method: 'delete'
  })
}
