import request from '@/utils/request'

// 查询前沿技术-文章列表
export function listTechnicalArticle(query) {
  return request({
    url: '/screen/technicalArticle/list',
    method: 'get',
    params: query
  })
}

// 查询前沿技术-文章详细
export function getTechnicalArticle(id) {
  return request({
    url: '/screen/technicalArticle/' + id,
    method: 'get'
  })
}

// 新增前沿技术-文章
export function addTechnicalArticle(data) {
  return request({
    url: '/screen/technicalArticle',
    method: 'post',
    data: data
  })
}

// 修改前沿技术-文章
export function updateTechnicalArticle(data) {
  return request({
    url: '/screen/technicalArticle/edit',
    method: 'post',
    data: data
  })
}

// 删除前沿技术-文章
export function delTechnicalArticle(data) {
  return request({
    url: '/screen/technicalArticle/remove',
    method: 'post',
    data: data
  })
}
