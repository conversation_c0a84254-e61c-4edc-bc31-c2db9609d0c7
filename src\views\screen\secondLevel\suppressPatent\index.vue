<template>
  <el-dialog :title="props.visibleTitle" v-model="props.visible" :width="props.visibleWidth" append-to-body
    :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="唯一标识" prop="suppressSn">
        <el-input v-model="queryParams.suppressSn" placeholder="请输入唯一标识" clearable @keyup.enter="handleQuery" />
      </el-form-item> -->
      <el-form-item label="专利名称" prop="patentName">
        <el-input v-model="queryParams.patentName" placeholder="请输入专利名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="专利号" prop="patentNumber">
        <el-input v-model="queryParams.patentNumber" placeholder="请输入专利号" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="授权日期" prop="publishTime">
        <el-date-picker clearable v-model="queryParams.publishTime" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择授权日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="申请日期" prop="filingTime">
        <el-date-picker clearable v-model="queryParams.filingTime" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择申请日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="删除时间" prop="deleteTime">
        <el-date-picker clearable v-model="queryParams.deleteTime" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择删除时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="删除人" prop="deleteBy">
        <el-input v-model="queryParams.deleteBy" placeholder="请输入删除人" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['screen:suppressPatent:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:suppressPatent:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:suppressPatent:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['screen:suppressPatent:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="suppressPatentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" prop="suppressSn" width="150" />
      <el-table-column label="专利名称" prop="patentName" width="300" show-overflow-tooltip />
      <el-table-column label="专利号" prop="patentNumber" width="200" show-overflow-tooltip />
      <el-table-column label="授权日期" align="center" prop="publishTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请日期" align="center" prop="filingTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.filingTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:suppressPatent:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:suppressPatent:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改大屏-打压风险-受压企业专利对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="suppressPatentRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="唯一标识" prop="suppressSn">
          <el-input v-model="form.suppressSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="专利名称" prop="patentName">
          <el-input v-model="form.patentName" placeholder="请输入专利名称" />
        </el-form-item>
        <el-form-item label="专利号" prop="patentNumber">
          <el-input v-model="form.patentNumber" placeholder="请输入专利号" />
        </el-form-item>
        <el-form-item label="授权日期" prop="publishTime">
          <el-date-picker clearable v-model="form.publishTime" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择授权日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="申请日期" prop="filingTime">
          <el-date-picker clearable v-model="form.filingTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择申请日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup name="SuppressPatent">
import { listSuppressPatent, getSuppressPatent, delSuppressPatent, addSuppressPatent, updateSuppressPatent } from "@/api/screen/suppressPatent";

const emit = defineEmits(['closePatent']);

const { proxy } = getCurrentInstance();

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visibleTitle: {
    type: String,
    default: "自定义弹窗",
  },
  visibleWidth: {
    type: Number,
    default: 1280,
  },
  sn: {
    type: String,
    default: '1',
  }
});

const suppressPatentList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    suppressSn: null,
    patentName: null,
    patentNumber: null,
    publishTime: null,
    filingTime: null,
    status: null,
    userId: null,
    deptId: null,
    deleteTime: null,
    deleteBy: null
  },
  rules: {
    // publishTime: [
    //   { required: true, message: "授权日期不能为空", trigger: "blur" }
    // ],
    // filingTime: [
    //   { required: true, message: "申请日期不能为空", trigger: "blur" }
    // ],
  }
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.visible,
  (newValue, oldValue) => {
    if (newValue) {
      data.queryParams.suppressSn = props.sn;
      getList();
    }
  }
);

/** 查询大屏-打压风险-受压企业专利列表 */
function getList() {
  loading.value = true;
  listSuppressPatent({ ...queryParams.value }).then(response => {
    suppressPatentList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    suppressSn: null,
    patentName: null,
    patentNumber: null,
    publishTime: null,
    filingTime: null,
    status: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteTime: null,
    deleteBy: null
  };
  proxy.resetForm("suppressPatentRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  data.form.suppressSn = props.sn
  title.value = "添加大屏-打压风险-受压企业专利";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getSuppressPatent(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改大屏-打压风险-受压企业专利";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["suppressPatentRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateSuppressPatent(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSuppressPatent(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  proxy.$modal.confirm('是否确认删除大屏-打压风险-受压企业专利编号为"' + _ids + '"的数据项？').then(function () {
    return delSuppressPatent(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/suppressPatent/export', {
    ...queryParams.value
  }, `suppressPatent_${new Date().getTime()}.xlsx`)
}

const handleClose = (done) => {
  emit('closePatent', false);
  done()
}
</script>
