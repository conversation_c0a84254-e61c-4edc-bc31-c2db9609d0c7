<template>
  <el-dialog :title="props.visibleTitle" v-model="props.visible" :width="props.visibleWidth" append-to-body
    :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="queryParams" ref="suppressSoftwareRef" :inline="true" v-show="showSearch" label-width="120px">
      <!-- <el-form-item label="唯一标识" prop="suppressSn">
        <el-input v-model="queryParams.suppressSn" placeholder="请输入唯一标识" clearable />
      </el-form-item> -->
      <el-form-item label="软件名称" prop="softwareName">
        <el-input v-model="queryParams.softwareName" placeholder="请输入软件名称" clearable />
      </el-form-item>
      <el-form-item label="软件简称" prop="softwareAbbreviation">
        <el-input v-model="queryParams.softwareAbbreviation" placeholder="请输入软件简称" clearable />
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        <el-input v-model="queryParams.version" placeholder="请输入版本号" clearable />
      </el-form-item>
      <el-form-item label="登记号" prop="registrationNo">
        <el-input v-model="queryParams.registrationNo" placeholder="请输入登记号" clearable />
      </el-form-item>
      <el-form-item label="开发完成日期" prop="completionDate">
        <el-date-picker clearable v-model="queryParams.completionDate" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择开发完成日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="首次发布日期" prop="firstReleaseDate">
        <el-date-picker clearable v-model="queryParams.firstReleaseDate" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择首次发布日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="登记日期" prop="registrationDate">
        <el-date-picker clearable v-model="queryParams.registrationDate" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择登记日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['screen:suppressSoftware:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:suppressSoftware:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:suppressSoftware:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['screen:suppressSoftware:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="suppressSoftwareList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" prop="suppressSn" width="150" />
      <el-table-column label="软件名称" prop="softwareName" width="300" show-overflow-tooltip />
      <el-table-column label="软件简称" prop="softwareAbbreviation" width="300" show-overflow-tooltip />
      <el-table-column label="版本号" align="center" prop="version" width="100" show-overflow-tooltip />
      <el-table-column label="登记号" align="center" prop="registrationNo" width="150" show-overflow-tooltip />
      <el-table-column label="开发完成日期" align="center" prop="completionDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.completionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="首次发布日期" align="center" prop="firstReleaseDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.firstReleaseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登记日期" align="center" prop="registrationDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.registrationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:suppressSoftware:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:suppressSoftware:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改大屏-打压风险-受压企业软件对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="suppressSoftwareRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="唯一标识" prop="suppressSn">
          <el-input v-model="form.suppressSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="软件名称" prop="softwareName">
          <el-input v-model="form.softwareName" placeholder="请输入软件名称" />
        </el-form-item>
        <el-form-item label="软件简称" prop="softwareAbbreviation">
          <el-input v-model="form.softwareAbbreviation" placeholder="请输入软件简称" />
          <!-- <editor :min-height="192" v-model="form.softwareAbbreviation" placeholder="请输入内容" /> -->
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本号" />
        </el-form-item>
        <el-form-item label="登记号" prop="registrationNo">
          <el-input v-model="form.registrationNo" placeholder="请输入登记号" />
        </el-form-item>
        <el-form-item label="开发完成日期" prop="completionDate">
          <el-date-picker clearable v-model="form.completionDate" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择开发完成日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="首次发布日期" prop="firstReleaseDate">
          <el-date-picker clearable v-model="form.firstReleaseDate" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择首次发布日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="登记日期" prop="registrationDate">
          <el-date-picker clearable v-model="form.registrationDate" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择登记日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup name="SuppressSoftware">
import { listSuppressSoftware, getSuppressSoftware, delSuppressSoftware, addSuppressSoftware, updateSuppressSoftware } from "@/api/screen/suppressSoftware";

const emit = defineEmits(['closeSoftware']);

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visibleTitle: {
    type: String,
    default: "自定义弹窗",
  },
  visibleWidth: {
    type: Number,
    default: 1280,
  },
  sn: {
    type: String,
    default: '1',
  }
});

const { proxy } = getCurrentInstance();

const suppressSoftwareList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    suppressSn: null,
    softwareName: null,
    softwareAbbreviation: null,
    version: null,
    registrationNo: null,
    completionDate: null,
    firstReleaseDate: null,
    registrationDate: null,
    status: null,
    userId: null,
    deptId: null,
    deleteTime: null
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.visible,
  (newValue, oldValue) => {
    if (newValue) {
      data.queryParams.suppressSn = props.sn;
      getList();
    }
  }
);

/** 查询大屏-打压风险-受压企业软件列表 */
function getList() {
  loading.value = true;
  listSuppressSoftware({ ...queryParams.value}).then(response => {
    suppressSoftwareList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    suppressSn: null,
    softwareName: null,
    softwareAbbreviation: null,
    version: null,
    registrationNo: null,
    completionDate: null,
    firstReleaseDate: null,
    registrationDate: null,
    status: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteTime: null
  };
  proxy.resetForm("suppressSoftwareRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("suppressSoftwareRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  data.form.suppressSn = props.sn;
  title.value = "添加大屏-打压风险-受压企业软件";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getSuppressSoftware(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改大屏-打压风险-受压企业软件";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["suppressSoftwareRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateSuppressSoftware(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSuppressSoftware(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  proxy.$modal.confirm('是否确认删除大屏-打压风险-受压企业软件编号为"' + _ids + '"的数据项？').then(function () {
    return delSuppressSoftware(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/suppressSoftware/export', {
    ...queryParams.value
  }, `suppressSoftware_${new Date().getTime()}.xlsx`)
}

const handleClose = (done) => {
  emit('closeSoftware', false);
  done()
}
</script>
