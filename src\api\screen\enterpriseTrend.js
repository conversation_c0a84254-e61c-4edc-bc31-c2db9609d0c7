import request from '@/utils/request'

// 查询大屏-关键企业趋势列表
export function listEnterpriseTrend(query) {
  return request({
    url: '/screen/enterpriseTrend/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-关键企业趋势详细
export function getEnterpriseTrend(id) {
  return request({
    url: '/screen/enterpriseTrend/' + id,
    method: 'get'
  })
}

// 新增大屏-关键企业趋势
export function addEnterpriseTrend(data) {
  return request({
    url: '/screen/enterpriseTrend',
    method: 'post',
    data: data
  })
}

// 修改大屏-关键企业趋势
export function updateEnterpriseTrend(data) {
  return request({
    url: '/screen/enterpriseTrend',
    method: 'put',
    data: data
  })
}

// 删除大屏-关键企业趋势
export function delEnterpriseTrend(id) {
  return request({
    url: '/screen/enterpriseTrend/' + id,
    method: 'delete'
  })
}
