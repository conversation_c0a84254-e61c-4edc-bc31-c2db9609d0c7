<template>
  <el-dialog :title="props.visibleTitle" v-model="props.visible" :width="props.visibleWidth" append-to-body
    :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="事件编码" prop="eventsSn">
        <el-input v-model="queryParams.eventsSn" placeholder="请输入事件编码" clearable @keyup.enter="handleQuery" />
      </el-form-item> -->
      <el-form-item label="分析标题" prop="analysisTitle">
        <el-input v-model="queryParams.analysisTitle" placeholder="请输入分析标题" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['screen:eventsAnalysis:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:eventsAnalysis:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:eventsAnalysis:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['screen:eventsAnalysis:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="eventsAnalysisList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" min-width="150" prop="eventsSn" />
      <el-table-column label="分析标题" min-width="300" show-overflow-tooltip prop="analysisTitle" />
      <!-- <el-table-column label="关键字" align="center"
        prop="analysisKeywords" />
      <el-table-column label="主要内容摘要" align="center" prop="analysisSummary" /> -->
      <el-table-column label="分析内容" width="300" show-overflow-tooltip prop="analysisContent" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:eventsAnalysis:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:eventsAnalysis:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改事件分析对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="eventsAnalysisRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="唯一标识" prop="eventsSn">
          <el-input v-model="form.eventsSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="分析标题" prop="analysisTitle">
          <el-input v-model="form.analysisTitle" placeholder="请输入分析标题" />
        </el-form-item>
        <!-- <el-form-item label="关键字，用于搜索，如“中新天津生态城”、“绿色发展示范区”、“实施方案”等，多个关键字可用逗号分隔。" prop="analysisKeywords">
          <el-input v-model="form.analysisKeywords" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="分析概要或主要内容摘要。" prop="analysisSummary">
          <el-input v-model="form.analysisSummary" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
        <el-form-item label="分析内容" prop="analysisContent">
          <editor v-model="form.analysisContent" :min-height="192" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup name="EventsAnalysis">
import { listEventsAnalysis, getEventsAnalysis, delEventsAnalysis, addEventsAnalysis, updateEventsAnalysis } from "@/api/screen/eventsAnalysis";

const emit = defineEmits(['closePatent']);

const { proxy } = getCurrentInstance();

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visibleTitle: {
    type: String,
    default: "自定义弹窗",
  },
  visibleWidth: {
    type: Number,
    default: 1280,
  },
  sn: {
    type: String,
    default: '1',
  }
});

const eventsAnalysisList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    eventsSn: null,
    analysisTitle: null,
    analysisKeywords: null,
    analysisSummary: null,
    analysisContent: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.visible,
  (newValue, oldValue) => {
    if (newValue) {
      data.queryParams.eventsSn = props.sn;
      getList();
    }
  }
);

/** 查询事件分析列表 */
function getList() {
  loading.value = true;
  listEventsAnalysis({ ...queryParams.value }).then(response => {
    eventsAnalysisList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    eventsSn: null,
    analysisTitle: null,
    analysisKeywords: null,
    analysisSummary: null,
    analysisContent: null,
    status: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    delFlag: null,
    deleteBy: null,
    deleteTime: null
  };
  proxy.resetForm("eventsAnalysisRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  data.form.eventsSn = props.sn
  title.value = "添加事件分析";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getEventsAnalysis(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改事件分析";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["eventsAnalysisRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateEventsAnalysis(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addEventsAnalysis(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  proxy.$modal.confirm('是否确认删除事件分析编号为"' + _ids + '"的数据项？').then(function () {
    return delEventsAnalysis(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/eventsAnalysis/export', {
    ...queryParams.value
  }, `eventsAnalysis_${new Date().getTime()}.xlsx`)
}
const handleClose = (done) => {
  emit('closeAnalysis', false);
  done()
}

getList();
</script>
