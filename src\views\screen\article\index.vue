<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="唯一标识" prop="sn">
        <el-input v-model="queryParams.sn" placeholder="请输入文章唯一标识" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入文章标题" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="数据源编码" prop="sourceSn">
        <el-input v-model="queryParams.sourceSn" placeholder="请输入数据源编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="数据源名称" prop="sourceName">
        <el-input v-model="queryParams.sourceName" placeholder="请输入数据源名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker clearable v-model="queryParams.publishTime" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="租户编码" prop="tenantSn">
        <el-input v-model="queryParams.tenantSn" placeholder="请输入租户编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectSn">
        <el-input v-model="queryParams.projectSn" placeholder="请输入项目编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="屏幕编码" prop="screenSn">
        <el-input v-model="queryParams.screenSn" placeholder="请输入屏幕编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="栏目编码" prop="columnSn">
        <el-input v-model="queryParams.columnSn" placeholder="请输入栏目编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['screen:article:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:article:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:article:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['screen:article:export']">导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          v-hasPermi="['screen:article:add']">网络安全技术信息导入</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="articleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" width="150" prop="sn" />
      <el-table-column label="文章标题" width="300" prop="title" show-overflow-tooltip />
      <el-table-column label="数据源编码" width="200" prop="sourceSn" show-overflow-tooltip />
      <el-table-column label="数据源名称" width="200" prop="sourceName" show-overflow-tooltip />
      <el-table-column label="原文连接" width="200" prop="originalUrl" show-overflow-tooltip />
      <el-table-column label="发布时间" align="center" prop="publishTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关键词" width="150" prop="keywords" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="租户编码" align="center" prop="tenantSn" show-overflow-tooltip/>
      <el-table-column label="项目编码" align="center" prop="projectSn" />
      <el-table-column label="屏幕编码" align="center" prop="screenSn" />
      <el-table-column label="栏目编码" align="center" prop="columnSn" />
      <el-table-column label="操作" align="center" fixed="right" min-width="150" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:article:edit']">修改
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:article:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改大屏-发布文章对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="articleRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="文章唯一标识" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入文章唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="文章标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入文章标题" />
        </el-form-item>
        <el-form-item label="数据源编码" prop="sourceSn">
          <el-input v-model="form.sourceSn" placeholder="请输入数据源编码" />
        </el-form-item>
        <el-form-item label="数据源名称" prop="sourceName">
          <el-input v-model="form.sourceName" placeholder="请输入数据源名称" />
        </el-form-item>
        <el-form-item label="原文连接" prop="originalUrl">
          <el-input v-model="form.originalUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker clearable v-model="form.publishTime" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择发布时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="文章内容">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-row>
          <el-col :span="12" v-if="userStore.roles.includes('admin')">
            <el-form-item label="租户编码" prop="tenantSn">
              <el-input v-model="form.tenantSn" placeholder="请输入租户编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="projectSn">
              <el-input v-model="form.projectSn" placeholder="请输入项目编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕编码" prop="screenSn">
              <el-input v-model="form.screenSn" placeholder="请输入屏幕编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="栏目编码" prop="columnSn">
              <el-input v-model="form.columnSn" placeholder="请输入栏目编码" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Article">
import { getToken } from "@/utils/auth";
import { addArticle, delArticle, getArticle, listArticle, updateArticle } from "@/api/screen/article";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const articleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    sn: null,
    title: null,
    sourceSn: null,
    sourceName: null,
    originalUrl: null,
    publishTime: null,
    keywords: null,
    content: null,
    author: null,
    orderNum: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    tenantSn: null,
    projectSn: null,
    screenSn: null,
    columnSn: null
  },
  rules: {
    projectSn: [
      { required: true, message: "项目编码不能为空", trigger: "blur" }
    ],
    // tenantSn: [
    //   { required: true, message: "租户编码不能为空", trigger: "blur" }
    // ],
    screenSn: [
      { required: true, message: "屏幕编码不能为空", trigger: "blur" }
    ],
    columnSn: [
      { required: true, message: "栏目编码不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  businessType: null,
  headers: { Authorization: "Bearer " + getToken() },
  url: ''
});

/** 查询大屏-发布文章列表 */
function getList() {
  loading.value = true;
  listArticle(queryParams.value).then(response => {
    articleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    sn: null,
    title: null,
    sourceSn: null,
    sourceName: null,
    originalUrl: null,
    publishTime: null,
    keywords: null,
    content: null,
    author: null,
    orderNum: null,
    status: null,
    delFlag: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    tenantSn: null,
    projectSn: null,
    screenSn: null,
    columnSn: null
  };
  proxy.resetForm("articleRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加大屏-发布文章";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getArticle(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改大屏-发布文章";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["articleRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateArticle(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addArticle(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  proxy.$modal.confirm('是否确认删除大屏-发布文章编号为"' + _ids + '"的数据项？').then(function () {
    return delArticle(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/article/export', {
    ...queryParams.value
  }, `article_${new Date().getTime()}.xlsx`)
}

/** 网络安全技术信息导入 */
function handleImport() {
  upload.title = "网络安全技术信息导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/article/excelImport";
  upload.businessType = 100;
  upload.open = true;
};

/** 下载模板操作 */
function importTemplate() {
  proxy.download("xty-screen/article/downloadTemplate", {
  }, `${upload.title}模版.xlsx`);
};

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

getList();
</script>
