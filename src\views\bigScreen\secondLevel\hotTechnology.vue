<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div v-show="show" class="custom-dialog" :style="{ width: props.width + 'px' }" @click.stop>
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title1">
            报告标题:
            <span> {{ content.reportName }}</span>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title1">
            发布机构:
            <span> {{ content.publishUnit }}</span>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title1">
            发布时间:
            <span>{{ content.publishTime }}</span>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">国内技术报告</div>
          <div class="bg-box-content">
            <div v-html="content.content"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script setup>
import { technicalReportDetail } from '@/api/bigScreen/index'

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "自定义弹窗",
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 800,
  },
  id: {
    type: Number,
    default: 1,
  },
});
const emits = defineEmits(["update:visible"]);

watch(() => props.visible, (newValue) => {
  if (newValue) {
    data.show = false
    technicalReportDetail({ reportSn: props.id }).then((res) => {
      data.content = res.data
      data.show = true
    })
  }
})

const data = reactive({
  content: {},
  show: false
})

const { content, show } = toRefs(data)

// 关闭弹窗的方法
const closeDialog = () => {
  emits("update:visible", false);
};

// 处理遮罩层点击事件
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};
</script>
  
<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;
        font-size: 16px;

        .bg-box-title {
          font-weight: bolder;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          color: #ffffff;
          white-space: pre-wrap;
        }

        .bg-box-title1 {
          font-weight: bold;
          font-size: 16px;
          color: #ffffff;
          height: 40px;
          line-height: 40px;
          margin-bottom: -5px;

          span {
            color: #A1A6AE;
            font-size: 14px;
            margin-left: 10px;
          }
        }

        .content-flex {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}
</style>
  