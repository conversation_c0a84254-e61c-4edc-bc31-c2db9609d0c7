<template>
  <div class="timeline">
    <div
      v-for="(event, index) in timelineEvents"
      :key="index"
      class="timeline-item"
    >
      <div
        class="timeline-node"
        :style="{ '--line-color': getLineColor(event, index % 2 === 1) }"
      ></div>
      <div
        class="description-box odd-box"
        v-if="index % 2 === 1"
        :style="{ '--line-color': getLineColor(event, index % 2 === 1) }"
      >
        <div class="date">{{ event.date }}</div>
        <div></div>
        <div class="timeline-content" style="justify-content: flex-start">
          <div
            v-for="(item, Index) in event.description"
            :key="Index"
            class="description"
            :class="'descriptionLeave' + getRiskLevelClass(item.riskLevel)"
            @click="openView(item)"
          >
            {{ item.enterpriseName }}
          </div>
        </div>
      </div>
      <div
        class="description-box even-box"
        v-else
        :style="{ '--line-color': getLineColor(event, index % 2 === 1) }"
      >
        <div class="timeline-content" style="justify-content: flex-end">
          <div
            v-for="(item, Index) in event.description"
            :key="Index"
            class="description"
            :class="'descriptionLeave' + getRiskLevelClass(item.riskLevel)"
            @click="openView(item)"
          >
            {{ item.enterpriseName }}
          </div>
        </div>
        <div></div>
        <div class="date">{{ event.date }}</div>
      </div>
    </div>
    <div class="timeline-arrow"></div>
  </div>
</template>

<script setup>
const leaveColor = {
  严重: "#EF5350",
  一般: "#FFB74D",
  较轻: "#81C8E8",
};

const riskLevelClassMap = {
  严重: 1,
  一般: 2,
  较轻: 3,
};

const emits = defineEmits(["openEnterpriseInformation"]);

const props = defineProps({
  timelineEvents: {
    type: Array,
    default: [],
  },
});

// const timelineEvents = ref([
//   {
//     date: "2024-10-30",
//     description: [
//       { leave: 2, title: '北京航天希尔测试技术有限公司' },
//       { leave: 2, title: '北京摩诘创新科技股份有限公司' },
//       { leave: 2, title: '苏州天工力学测试技术有限公司' },
//     ]
//   },
//   {
//     date: "2024-12-02",
//     description: [
//       { leave: 1, title: '至微半导体(上海)有限公司' },
//       { leave: 1, title: '张江实验室' },
//       { leave: 2, title: '上海御微半导体技术有限公司' },
//     ]
//   },
//   {
//     date: "2025-3-25",
//     description: [
//       { leave: 1, title: '中国航空工业集团' },
//       { leave: 1, title: '浪潮软件有限公司' },
//       { leave: 1, title: '浪潮电子信息产业股份有限公司' },
//     ]
//   },
// ]);

const getRiskLevelClass = (riskLevel) => {
  return riskLevelClassMap[riskLevel] || 1; // 默认返回1（最轻级别）
};

const getLineColor = (event, type) => {
  if (event.description && event.description.length > 0) {
    const lastItem = type
      ? event.description[0]
      : event.description[event.description.length - 1];
    if (leaveColor[lastItem.riskLevel]) {
      return leaveColor[lastItem.riskLevel];
    }
  }
  return "#000000";
};

const openView = (item) => {
  emits("openEnterpriseInformation", item);
};
</script>

<style scoped lang="scss">
.timeline {
  position: relative;
  display: flex;
  justify-content: center;
  height: 100%;
  padding: 10px 15px;

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: calc(5% + 0px);
    width: calc(90% - 0px);
    height: 2px;
    background: #ffffff;
    transform: translateY(-50%);
    z-index: 0;
  }

  .timeline-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 33.3%;

    &:last-child::after {
      display: none;
    }

    .timeline-node {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: var(--line-color);
      z-index: 2;
    }

    .description-box {
      position: relative;
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .date {
        position: absolute;
        top: calc(50% + 30px);
        transform: translateY(-50%);
        font-size: 16px;
        color: #ffffff;
        font-weight: bold;
      }

      .timeline-content {
        margin-left: -40px;
        min-width: 100px;
        max-width: calc(100% - 40px);
        height: 96px;
        position: relative;
        text-align: center;
        display: flex;
        flex-direction: column;
        z-index: 3;

        .description {
          max-width: 170px;
          font-weight: 400;
          font-size: 12px;
          line-height: 30px;
          height: 30px;
          width: fit-content;
          padding: 0 10px;
          color: #ffffff;
          text-align: left;
          max-height: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          transition: transform 0.3s ease;
          background-size: 100% 100%;
          cursor: pointer;

          &:hover {
            transform: translateX(5px);
          }
        }

        .descriptionLeave1 {
          background-image: url("@/assets/bigScreen/leave1.png");
        }

        .descriptionLeave2 {
          background-image: url("@/assets/bigScreen/leave2.png");
        }

        .descriptionLeave3 {
          background-image: url("@/assets/bigScreen/leave3.png");
        }
      }

      &::before {
        content: "";
        position: absolute;
        bottom: calc(50% + 6px);
        left: 50%;
        width: 2px;
        height: calc(50% - 102px);
        z-index: 2;
        background: var(--line-color);
      }
    }

    .odd-box {
      .date {
        top: calc(50% - 30px);
      }

      &::before {
        bottom: 0;
        top: calc(50% + 6px);
      }
    }
  }

  .timeline-arrow {
    position: absolute;
    top: 50%;
    left: calc(90% + 20px);
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 12px solid #ffffff;
    z-index: 1;
  }
}
</style>
