<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :style="{ width: props.width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">受打压情况</div>
          <div class="bg-box-content">
            <div ref="chartKeyDetails" style="width: 100%; height: 300px"></div>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">受打压企业</div>
          <div class="bg-box-content">
            <el-table
              :row-style="getRowStyle"
              :data="enterpriseList"
              border
              style="width: 100%"
            >
              <el-table-column
                prop="publishTime"
                align="center"
                label="时间"
                width="180"
              />
              <el-table-column
                prop="enterpriseName"
                align="center"
                label="企业名称"
                min-width="180"
              >
                <template #default="scope">
                  <div @click="openDetail(scope.row)" style="cursor: pointer">
                    {{ scope.row.enterpriseName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="domain"
                align="center"
                label="领域"
                width="200"
              />
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { defineProps, defineEmits, computed, watch, nextTick } from "vue";
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1200,
  },
  item: {
    type: Object,
    default: () => ({}),
  },
  guanjianType: {
    type: Number,
    default: 1,
  },
  levelCount: {
    type: Object,
    default: () => [],
  },
  enterpriseList: {
    type: Array,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
});
const emits = defineEmits([
  "update:visible",
  "openEnterpriseInformation",
  "pagination",
]);

var myChart = null;
const option = ref({});
const chartKeyDetails = ref(null);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);

watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      nextTick(() => {
        if (chartKeyDetails.value) {
          myChart = echarts.init(chartKeyDetails.value);
          initChart();
        }
      });
    }
  }
);

const initChart = () => {
  option.value = {
    legend: {
      show: true,
      textStyle: { color: "#fff" },
      padding: [10, 10],
      x: "center",
    },
    tooltip: {
      show: true,
      trigger: "axis",
      showContent: true,
      triggerOn: "mousemove",
      // axisPointer: {
      //   type: "shadow",
      // },
    },
    dataset: {
      dimensions: ["product", "严重", "一般", "较轻"],
      source: props.levelCount,
    },
    xAxis: {
      type: "category",
      axisLabel: {
        fontSize: "14px",
        color: "#fff",
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        show: true,
        lineStyle: {
          color: "#ffffff70",
          type: "dotted",
        },
      },
      axisLabel: {
        interval: 0,
        fontSize: "14px",
        color: "#fff",
      },
    },
    series: [
      { type: "bar", itemStyle: { color: "#A82B2D" },barMaxWidth:40, },
      { type: "bar", itemStyle: { color: "#AC6628" },barMaxWidth:40, },
      { type: "bar", itemStyle: { color: "#345CA4" },barMaxWidth:40, },
    ],
  };

  setTimeout(() => {
    myChart?.resize();
  }, 1);
  myChart.setOption(option.value);
};

const getRowStyle = (rowInfo) => {
  const { row } = rowInfo;
  switch (row.riskLevel) {
    case '严重':
      return "background-color: #A82B2D;color:#ffffff";
    case '一般':
      return "background-color: #AC6628;color:#ffffff"; // 一般，橙色
    case '较轻':
      return "background-color: #345CA4;color:#ffffff"; // 较轻，绿色
  }
};

const openDetail = (item) => {
  emits("openEnterpriseInformation", item);
};

const getList = () => {
  emits("pagination", queryParams.value);
};

// 关闭弹窗的方法
const closeDialog = () => {
  emits("update:visible", false);
};

// 处理遮罩层点击事件
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          .bg-box-img {
            width: 120px;
            height: 120px;
            background: #fff;
            vertical-align: middle;
            position: relative;

            img {
              width: 120px;
              height: 120px;
              display: inline-block;
            }
          }

          .bg-box-summary {
            margin-left: 20px;
          }
        }

        .flex-box {
          display: flex;
        }
      }
    }
  }
}

:deep(.el-table__header th) {
  background-color: #1f3850 !important;
  color: rgba(255, 255, 255);
  font-size: 16px;
}

:deep(.el-table__body td) {
  font-size: 14px;
}

:deep(.el-table__body tr:hover > td) {
  background-color: #132f5600 !important;
}

:deep(.pagination-container) {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump) {
  color: #f2f2f2;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination button:disabled) {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

:deep(.el-pager li) {
  background: #ffffff00 !important;
  color: #fff !important;

  &.is-active {
    color: #1890ff !important;
  }
}
</style>
