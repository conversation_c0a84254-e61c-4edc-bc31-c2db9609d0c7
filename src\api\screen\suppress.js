import request from '@/utils/request'

// 查询大屏-打压风险列表
export function listSuppress(query) {
  return request({
    url: '/screen/suppress/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-打压风险详细
export function getSuppress(id) {
  return request({
    url: '/screen/suppress/' + id,
    method: 'get'
  })
}

// 新增大屏-打压风险
export function addSuppress(data) {
  return request({
    url: '/screen/suppress',
    method: 'post',
    data: data
  })
}

// 修改大屏-打压风险
export function updateSuppress(data) {
  return request({
    url: '/screen/suppress/edit',
    method: 'post',
    data: data
  })
}

// 删除大屏-打压风险
export function delSuppress(data) {
  return request({
    url: '/screen/suppress/remove',
    method: 'post',
    data: data
  })
}
