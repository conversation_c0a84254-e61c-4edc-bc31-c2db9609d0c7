import request from '@/utils/request'

// 查询人工智能架构文章列表
export function listAiArticle(query) {
  return request({
    url: '/screen/aiArticle/list',
    method: 'get',
    params: query
  })
}

// 查询人工智能架构文章详细
export function getAiArticle(id) {
  return request({
    url: '/screen/aiArticle/' + id,
    method: 'get'
  })
}

// 新增人工智能架构文章
export function addAiArticle(data) {
  return request({
    url: '/screen/aiArticle',
    method: 'post',
    data: data
  })
}

// 修改人工智能架构文章
export function updateAiArticle(data) {
  return request({
    url: '/screen/aiArticle/edit',
    method: 'post',
    data: data
  })
}

// 删除人工智能架构文章
export function delAiArticle(data) {
  return request({
    url: '/screen/aiArticle/remove',
    method: 'post',
    data: data
  })
}
