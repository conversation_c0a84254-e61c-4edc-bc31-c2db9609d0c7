import request from '@/utils/request'

// 查询大屏-风险态势列表
export function listTrend(query) {
  return request({
    url: '/screen/trend/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-风险态势详细
export function getTrend(id) {
  return request({
    url: '/screen/trend/' + id,
    method: 'get'
  })
}

// 新增大屏-风险态势
export function addTrend(data) {
  return request({
    url: '/screen/trend',
    method: 'post',
    data: data
  })
}

// 修改大屏-风险态势
export function updateTrend(data) {
  return request({
    url: '/screen/trend/edit',
    method: 'post',
    data: data
  })
}

// 删除大屏-风险态势
export function delTrend(data) {
  return request({
    url: '/screen/trend/remove',
    method: 'post',
    data: data
  })
}
