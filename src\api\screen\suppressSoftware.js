import request from '@/utils/request'

// 查询大屏-打压风险-受压企业软件列表
export function listSuppressSoftware(query) {
  return request({
    url: '/screen/suppressSoftware/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-打压风险-受压企业软件详细
export function getSuppressSoftware(id) {
  return request({
    url: '/screen/suppressSoftware/' + id,
    method: 'get'
  })
}

// 新增大屏-打压风险-受压企业软件
export function addSuppressSoftware(data) {
  return request({
    url: '/screen/suppressSoftware',
    method: 'post',
    data: data
  })
}

// 修改大屏-打压风险-受压企业软件
export function updateSuppressSoftware(data) {
  return request({
    url: '/screen/suppressSoftware/edit',
    method: 'post',
    data: data
  })
}

// 删除大屏-打压风险-受压企业软件
export function delSuppressSoftware(data) {
  return request({
    url: '/screen/suppressSoftware/remove',
    method: 'post',
    data: data
  })
}
