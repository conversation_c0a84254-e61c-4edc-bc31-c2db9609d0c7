<template>
  <div class="app-container" style="display: flex;">
    <div style="background-color: #ffffff;width: 300px;">
      <div class="head-container">
        <el-input v-model="tenantName" placeholder="请输入租户名称" clearable prefix-icon="Search" style="margin-bottom: 20px" />
      </div>
      <div class="head-container">
        <el-tree :data="tenantNameLists" :props="{ children: 'children', label: 'tenantName' }"
          :expand-on-click-node="false" :filter-node-method="filterNode" ref="tree" node-key="tenantSn" default-expand-all
          highlight-current @node-click="handleNodeClick" />
      </div>
    </div>
    <div style="width: calc(100% - 300px);padding-left: 15px;">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch"
        style="background-color: #ffffff;padding: 20px 5px 0;">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="queryParams.roleName" placeholder="请输入角色名称" clearable style="width: 240px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="权限字符" prop="roleKey">
          <el-input v-model="queryParams.roleKey" placeholder="请输入权限字符" clearable style="width: 240px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="角色状态" clearable style="width: 240px">
            <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8" style="background-color: #ffffff;margin: 0 0 15px;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:role:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:role:edit']">修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:role:remove']">删除
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:role:export']">导出
          </el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="角色编号" prop="roleId" width="80" align="center" />
        <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="租户名称" prop="tenantName" width="150" :show-overflow-tooltip="true" />
        <el-table-column label="权限字符" prop="roleKey" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="显示顺序" prop="roleSort" width="80" :show-overflow-tooltip="true" align="center" />
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
              @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
          <template #default="scope">
            <template v-if="scope.row.roleId !== 1">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['system:role:edit']">修改
              </el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['system:role:remove']">删除
              </el-button>
              <el-dropdown @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['system:role:edit']">
                <el-button link type="primary" icon="DArrowRight">更多</el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="handleDataScope" icon="CircleCheck">数据权限</el-dropdown-item>
                    <el-dropdown-item command="handleAuthUser" icon="User">分配用户</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </div>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="租户名称" prop="tenantSn" v-if="tenantSn == 0">
          <el-select style="width: 100%;" v-model="form.tenantSn" placeholder="请选择租户名称" @change="getTenantMenusFun"
            :disabled="title == '修改角色'">
            <el-option v-for="item in tenantNameList" :key="item.tenantSn" :label="item.tenantName"
              :value="item.tenantSn" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色顺序" prop="roleSort">
          <el-input-number v-model="form.roleSort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{
              dict.label
            }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
          <el-checkbox v-model="form.menuCheckStrictly" @change="handleCheckedTreeConnect($event, 'menu')">父子联动
          </el-checkbox>
          <el-tree class="tree-border" :data="menuOptions" show-checkbox ref="menuRef" node-key="id"
            :check-strictly="!form.menuCheckStrictly" empty-text="加载中，请稍候" :props="defaultProps"></el-tree>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 分配角色数据权限对话框 -->
    <el-dialog :title="title" v-model="openDataScope" width="500px" append-to-body>
      <el-form :model="form" label-width="80px">
        <el-form-item label="角色名称">
          <el-input v-model="form.roleName" :disabled="true" />
        </el-form-item>
        <el-form-item label="权限字符">
          <el-input v-model="form.roleKey" :disabled="true" />
        </el-form-item>
        <el-form-item label="权限范围">
          <el-select v-model="form.dataScope" @change="dataScopeSelectChange">
            <el-option v-for="item in dataScopeOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据权限" v-show="form.dataScope == 2">
          <el-checkbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
          <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</el-checkbox>
          <el-checkbox v-model="form.deptCheckStrictly" @change="handleCheckedTreeConnect($event, 'dept')">父子联动
          </el-checkbox>
          <el-tree class="tree-border" :data="deptOptions" show-checkbox default-expand-all ref="dept" node-key="id"
            :check-strictly="!form.deptCheckStrictly" empty-text="加载中，请稍候" :props="defaultProps"></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDataScope">确 定</el-button>
        <el-button @click="cancelDataScope">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  addRole,
  changeRoleStatus,
  dataScope,
  delRole,
  deptTreeSelect,
  getRole,
  getTenantMenus,
  getTenantNameList,
  listRole,
  updateRole
} from "@/api/system/role";
import { roleMenuTreeselect, treeselect as menuTreeselect } from "@/api/system/menu";
import useUserStore from '@/store/modules/user'

const userStore = useUserStore();
const tenantSn = ref(0);

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

// 响应式数据
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const roleList = ref([]);
const title = ref("");
const open = ref(false);
const openDataScope = ref(false);
const menuExpand = ref(false);
const menuNodeAll = ref(false);
const deptExpand = ref(true);
const deptNodeAll = ref(false);
const dateRange = ref([]);
const dataScopeOptions = [
  {
    value: "1",
    label: "全部数据权限"
  },
  {
    value: "2",
    label: "自定数据权限"
  },
  {
    value: "3",
    label: "本部门数据权限"
  },
  {
    value: "4",
    label: "本部门及以下数据权限"
  },
  {
    value: "5",
    label: "仅本人数据权限"
  }
];
const menuOptions = ref([]);
const deptOptions = ref([]);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  roleName: undefined,
  roleKey: undefined,
  status: undefined
});
const form = reactive({
  roleId: undefined,
  roleName: undefined,
  roleKey: undefined,
  roleSort: 0,
  status: "0",
  menuIds: [],
  deptIds: [],
  menuCheckStrictly: true,
  deptCheckStrictly: true,
  remark: undefined
});
const defaultProps = {
  children: "children",
  label: "label"
};
const rules = {
  roleName: [
    { required: true, message: "角色名称不能为空", trigger: "blur" }
  ],
  roleSort: [
    { required: true, message: "角色顺序不能为空", trigger: "blur" }
  ]
};
const tenantNameList = ref([]);
const tenantNameLists = ref([]);
const tenantName = ref(null);

// 引用
const tree = ref(null);
const menuRef = ref(null);
const dept = ref(null);
const formRef = ref(null);
const queryForm = ref(null);

// 监听 tenantName 变化
watch(tenantName, (val) => {
  if (tree.value) {
    tree.value.filter(val);
  }
});

// 生命周期钩子
onMounted(() => {
  tenantSn.vaule = userStore.tenantSn
  if (tenantSn.vaule > 0) {
    // getTenantMenusFun(tenantSn.vaule);
  } else {
    getTenantNameListFun();
  }
  getList();
});

// 方法
const getList = () => {
  loading.value = true;
  listRole(proxy.addDateRange(queryParams, dateRange.value)).then(response => {
    roleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

const getTenantNameListFun = () => {
  getTenantNameList().then(response => {
    tenantNameList.value = response.data;
    tenantNameLists.value = [{ tenantSn: null, tenantName: '租户列表', children: response.data }];
  });
};

const getTenantMenusFun = (tenantSn) => {
  form.tenantSn = tenantSn;
  getTenantMenus(form.tenantSn).then(response => {
    if (menuRef.value) {
      menuRef.value.setCheckedKeys([]);
    }
    menuOptions.value = response.data;
  });
};

const getMenuTreeselect = () => {
  menuTreeselect().then(response => {
    menuOptions.value = response.data;
  });
};

const getMenuAllCheckedKeys = () => {
  if (menuRef.value) {
    let checkedKeys = menuRef.value.getCheckedKeys();
    let halfCheckedKeys = menuRef.value.getHalfCheckedKeys();
    checkedKeys.unshift(...halfCheckedKeys);
    return checkedKeys;
  }
  return [];
};

const getDeptAllCheckedKeys = () => {
  if (dept.value) {
    let checkedKeys = dept.value.getCheckedKeys();
    let halfCheckedKeys = dept.value.getHalfCheckedKeys();
    checkedKeys.unshift(...halfCheckedKeys);
    return checkedKeys;
  }
  return [];
};

const getRoleMenuTreeselect = (roleId) => {
  return roleMenuTreeselect(roleId).then(response => {
    menuOptions.value = response.menus;
    return response;
  });
};

const getDeptTree = (roleId) => {
  return deptTreeSelect(roleId).then(response => {
    deptOptions.value = response.depts;
    return response;
  });
};

const handleStatusChange = (row) => {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.roleName + '"角色吗？').then(() => {
    return changeRoleStatus(row.roleId, row.status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(() => {
    row.status = row.status === "0" ? "1" : "0";
  });
};

const cancel = () => {
  open.value = false;
  reset();
};

const cancelDataScope = () => {
  openDataScope.value = false;
  reset();
};

const reset = () => {
  console.log(menuRef.value);
  if (menuRef.value) {
    menuRef.value.setCheckedKeys([]);
  }
  menuExpand.value = false;
  menuNodeAll.value = false;
  deptExpand.value = true;
  deptNodeAll.value = false;
  form.roleId = undefined;
  form.roleName = undefined;
  form.roleKey = undefined;
  form.roleSort = 0;
  form.status = "0";
  form.menuIds = [];
  form.deptIds = [];
  form.menuCheckStrictly = true;
  form.deptCheckStrictly = true;
  form.remark = undefined;
  proxy.resetForm("formRef");
};

const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const resetQuery = () => {
  dateRange.value = [];
  proxy.resetForm("queryForm");
  handleQuery();
};

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.roleId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

const handleCommand = (command, row) => {
  switch (command) {
    case "handleDataScope":
      handleDataScope(row);
      break;
    case "handleAuthUser":
      handleAuthUser(row);
      break;
    default:
      break;
  }
};

const handleCheckedTreeExpand = (value, type) => {
  if (type === 'menu') {
    let treeList = menuOptions.value;
    for (let i = 0; i < treeList.length; i++) {
      if (menuRef.value) {
        menuRef.value.store.nodesMap[treeList[i].id].expanded = value;
      }
    }
  } else if (type === 'dept') {
    let treeList = deptOptions.value;
    for (let i = 0; i < treeList.length; i++) {
      if (dept.value) {
        dept.value.store.nodesMap[treeList[i].id].expanded = value;
      }
    }
  }
};

const handleCheckedTreeNodeAll = (value, type) => {
  if (type === 'menu') {
    if (menuRef.value) {
      menuRef.value.setCheckedNodes(value ? menuOptions.value : []);
    }
  } else if (type === 'dept') {
    if (dept.value) {
      dept.value.setCheckedNodes(value ? deptOptions.value : []);
    }
  }
};

const handleCheckedTreeConnect = (value, type) => {
  if (type === 'menu') {
    form.menuCheckStrictly = value;
  } else if (type === 'dept') {
    form.deptCheckStrictly = value;
  }
};

const handleAdd = () => {
  // getMenuTreeselect();
  getTenantMenusFun(tenantSn.value);
  open.value = true;
  title.value = "添加角色";
};

const handleUpdate = async (row) => {
  reset();
  const roleId = row.roleId || ids.value;
  const roleMenu = getRoleMenuTreeselect(roleId);
  getRole(roleId).then(async response => {
    Object.assign(form, response.data);
    form.menuCheckStrictly = true;
    console.log(form);
    await getTenantMenusFun(form.tenantSn);
    open.value = true;
    setTimeout(() => {
      roleMenu.then(res => {
        let checkedKeys = res.checkedKeys;
        checkedKeys.forEach((v) => {
          if (menuRef.value) {
            menuRef.value.setChecked(v, true, false);
          }
        });
      });
    }, 300);
    title.value = "修改角色";
  });
};

const dataScopeSelectChange = (value) => {
  if (value !== '2') {
    if (dept.value) {
      dept.value.setCheckedKeys([]);
    }
  }
};

const handleDataScope = (row) => {
  reset();
  const deptTreeSelect = getDeptTree(row.roleId);
  getRole(row.roleId).then(response => {
    Object.assign(form, response.data);
    openDataScope.value = true;
    deptTreeSelect.then(res => {
      if (dept.value) {
        dept.value.setCheckedKeys(res.checkedKeys);
      }
    });
    title.value = "分配数据权限";
  });
};

const handleAuthUser = (row) => {
  const roleId = row.roleId;
  proxy.$router.push("/system/role-auth/user/" + roleId);
};

const submitForm = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      if (form.roleId !== undefined) {
        form.menuIds = getMenuAllCheckedKeys();
        updateRole(form).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        form.menuIds = getMenuAllCheckedKeys();
        addRole(form).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
};

const submitDataScope = () => {
  if (form.roleId !== undefined) {
    form.deptIds = getDeptAllCheckedKeys();
    dataScope(form).then(response => {
      proxy.$modal.msgSuccess("修改成功");
      openDataScope.value = false;
      getList();
    });
  }
};

const handleDelete = (row) => {
  const roleIds = row.roleId || ids.value;
  proxy.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(() => {
    return delRole(roleIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
};

const handleExport = () => {
  download('system/role/export', {
    ...queryParams, ids: ids.value.join(',')
  }, `role_${new Date().getTime()}.xlsx`);
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.tenantName.indexOf(value) !== -1;
};

const handleNodeClick = (data) => {
  queryParams.deptId = data.id;
  queryParams.tenantSn = data.tenantSn;
  handleQuery();
};
</script>