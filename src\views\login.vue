<template>
  <div class="login">
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">{{ title }}</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
          <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码"
          @keyup.enter="handleLogin">
          <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="smsCode" v-if="captchaEnabled">
        <div style="display: flex;justify-content: space-between;width: 100%;">
          <el-input v-model="loginForm.smsCode" auto-complete="off" placeholder="验证码" style="width: 63%"
            @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </el-input>
          <div class="login-code">
            <el-button @click="getSmsCodeFun" type="primary" v-if="showCode" style="height: 38px">获取验证码</el-button>
            <el-button type="primary" disabled v-else style="height: 38px" key="resenBtn">{{ count }} s后重发</el-button>
          </div>
        </div>
      </el-form-item>
      <!-- <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          size="large"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter="handleLogin"
        >
          <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img"/>
        </div>
      </el-form-item> -->
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <router-link style="margin:0px 0px 25px 0px;float: right;font-size: 14px;" class="link-type"
        :to="'/retrieve'">忘记密码</router-link>
      <el-form-item style="width:100%;">
        <el-button :loading="loading" size="large" type="primary" style="width:100%;" @click.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
    </div>
    <!-- 参数配置提醒 -->
    <el-dialog title="提示" v-model="dialogVisible" width="30%" :close-on-click-modal="false">
      <div style="padding: 10px 0 30px;">{{ promptInfo }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { getCodeImg, getSmsCode } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'
import { reactive, toRefs } from "vue";
import { getConfigKey, getConfig } from "@/api/system/config";

const userStore = useUserStore()
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: ""
});
const status = reactive({
  count: 60,
  showCode: true,
  timer: null,
  promptInfo: '',
  dialogVisible: false,
  title: '后台管理系统',
})
const { count, showCode, timer, promptInfo, dialogVisible, title } = toRefs(status)

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [
    { required: true, trigger: "blur", message: "请输入您的密码" },
  ],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref("");
const loading = ref(false);
// 图形验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(route, (newRoute) => {
  redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      let reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{8,20}$/
      loading.value = true;
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      let form = JSON.parse(JSON.stringify(loginForm.value))
      userStore.login(form).then(() => {
        const query = route.query;
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
        if (!reg.test(loginForm.value.password)) {
          proxy.$alert('请尽快修改您的密码，密码位数为8-20位且必须包含大小写字母数字加符号', '系统提示', {
            confirmButtonText: '确定',
            callback: action => {
              // router.push({ path: redirect.value || "/", query: otherQueryParams });
              router.push({ path: "/transfer" })
            }
          });
        } else {
          router.push({ path: "/transfer" })
          // router.push({ path: redirect.value || "/", query: otherQueryParams });
        }
      }).catch(() => {
        loading.value = false;
      });
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

// 获取手机验证码
const getSmsCodeFun = () => {
  let data = {
    username: loginForm.value.username,
    password: encrypt(loginForm.value.password),
  };
  if (loginForm.value.username && loginForm.value.password) {
    getSmsCode(data).then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess(res.data);
        // loginForm.value.smsUuid = res.data;
        const TIME_COUNT = 60;
        if (!status.timer) {
          status.count = TIME_COUNT;
          status.showCode = false;
          status.timer = setInterval(() => {
            if (status.count > 0 && status.count <= TIME_COUNT) {
              status.count--;
            } else {
              status.showCode = true;
              clearInterval(status.timer);
              status.timer = null;
            }
          }, 1000);
        }
      } else {
        proxy.$modal.msgError(res.msg);
      }
    });
  } else {
    if (!loginForm.value.username && loginForm.value.password) {
      proxy.$modal.msgError("用户名不能为空！");
    } else if (!loginForm.value.password && loginForm.value.username) {
      proxy.$modal.msgError("密码不能为空！");
    } else if (!loginForm.value.username && !loginForm.value.password) {
      proxy.$modal.msgError("用户名、密码不能为空！");
    }
  }
}

// 获取参数配置
const getConfigFun = () => {
  getConfig(103).then((res) => {
    if (res.code === 200) {
      if (res.data.configValue && res.data.configValue === "true") {
        if (!status.dialogVisible) {
          status.promptInfo = res.data.remark;
          status.dialogVisible = true;
        }
      }
    }
  })
}

getConfigKey('sys.account.captchaEnabled').then((res) => {
  if (res.code === 200) {
    captchaEnabled.value = res.msg == "false" ? false : true
  }
})

getConfigKey('systemName').then(res => {
  if (res.code === 200) {
    title.value = res.msg;
  }
})

// getCode();
getCookie();
getConfigFun()
</script>

<style lang='scss' scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
