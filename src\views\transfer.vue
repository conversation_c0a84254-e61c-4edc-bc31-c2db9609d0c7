<template>
  <div class="app-container home">
  </div>
</template>

<script setup name="transfer">
import { useRouter } from 'vue-router';
import useUserStore from '@/store/modules/user'

const userStore = useUserStore();
const router = useRouter();

const url = ref(userStore.defaultHome);

if (url.value) {
  const hasRoute = computed(() => {
    return router.hasRoute(url.value);
  });

  if (hasRoute.value) {
    router.push(url.value);
  } else {
    router.push('/index');
  }
} else {
  router.push('/index');
}
</script>

<style scoped lang="scss">

</style>

