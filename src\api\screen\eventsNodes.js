import request from '@/utils/request'

// 查询热点事件列表
export function listEventsNodes(query) {
  return request({
    url: '/screen/eventsNodes/list',
    method: 'get',
    params: query
  })
}

// 查询热点事件详细
export function getEventsNodes(id) {
  return request({
    url: '/screen/eventsNodes/' + id,
    method: 'get'
  })
}

// 新增热点事件
export function addEventsNodes(data) {
  return request({
    url: '/screen/eventsNodes',
    method: 'post',
    data: data
  })
}

// 修改热点事件
export function updateEventsNodes(data) {
  return request({
    url: '/screen/eventsNodes',
    method: 'post',
    data: data
  })
}

// 删除热点事件
export function delEventsNodes(data) {
  return request({
    url: '/screen/eventsNodes/remove',
    method: 'post',
    data: data
  })
}
