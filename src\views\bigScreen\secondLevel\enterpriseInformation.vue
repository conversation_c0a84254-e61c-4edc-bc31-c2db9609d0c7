<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :style="{ width: props.width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">一、企业基本信息</div>
          <div class="bg-box-content">
            <div class="bg-box-content-list">
              <span>企业名称：</span>
              {{ content.enterpriseName }}
            </div>
            <div class="bg-box-content-list">
              <span>企业简介：</span>
              <div v-if="content.summary" v-html="content.summary.replace(/\n/g, '<br/>')"></div>
            </div>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">二、企业相关新闻</div>
          <div class="bg-box-content">
            <div v-if="content.news" v-html="content.news.replace(/\n/g, '<br/>')"></div>
          </div>
        </div>
        <div class="bg-box" v-if="total1 > 0">
          <div class="bg-box-title">三、企业相关知识产权</div>
          <div class="bg-box-content">
            <el-table :data="patentList" border style="width: 100%">
              <el-table-column
                prop="patentName"
                align="center"
                label="专利名称"
                width="300"
              >
                <template #default="scope">
                  <el-tooltip
                    effect="light"
                    :content="scope.row.patentName"
                    placement="top"
                    :hide-after="0"
                  >
                    <div class="ellipsis-text">{{ scope.row.patentName }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                prop="patentNumber"
                align="center"
                label="专利号"
              />
              <el-table-column
                prop="publishTime"
                align="center"
                label="申请日期"
              />
              <el-table-column
                prop="filingTime"
                align="center"
                label="授权日期"
              />
            </el-table>
            <pagination
              v-show="total1 > 0"
              :total="total1"
              v-model:page="queryParams1.pageNum"
              v-model:limit="queryParams1.pageSize"
              @pagination="getList1"
            />
          </div>
        </div>
        <div class="bg-box" style="padding-top: 16px" v-if="total2 > 0">
          <div class="bg-box-title" v-if="total1 == 0">
            三、企业相关知识产权
          </div>
          <div class="bg-box-content">
            <el-table :data="softwareList" border style="width: 100%">
              <el-table-column
                prop="softwareName"
                align="center"
                label="软件全称"
                width="300"
              >
                <template #default="scope">
                  <el-tooltip
                    effect="light"
                    :content="scope.row.softwareName"
                    placement="top"
                    :hide-after="0"
                  >
                    <div class="ellipsis-text">
                      {{ scope.row.softwareName }}
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                prop="softwareAbbreviation"
                align="center"
                label="软件简称"
              />
              <el-table-column
                prop="registrationNo"
                align="center"
                label="登记号"
              />
              <el-table-column
                prop="completionDate"
                align="center"
                label="开发完成日期"
              />
              <el-table-column
                prop="firstReleaseDate"
                align="center"
                label="首次发布日期"
              />
              <el-table-column
                prop="registrationDate"
                align="center"
                label="登记日期"
              />
            </el-table>
            <pagination
              v-show="total2 > 0"
              :total="total2"
              v-model:page="queryParams2.pageNum"
              v-model:limit="queryParams2.pageSize"
              @pagination="getList2"
            />
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">
            {{ total1 > 0 && total2 > 0 ? "四" : "三" }}、针对上述内容的观点
          </div>
          <div class="bg-box-content">
            <div v-if="content.viewpoint" v-html="content.viewpoint.replace(/\n/g, '<br/>')"></div>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">
            {{ total1 > 0 && total2 > 0 ? "五" : "四" }}、被打压后对企业的影响
          </div>
          <div class="bg-box-content">
            <div v-if="content.impact" v-html="content.impact.replace(/\n/g, '<br/>')"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed, watch } from "vue";

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "自定义弹窗",
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1000,
  },
  content: {
    type: Object,
    default: () => ({}),
  },
  total1: {
    type: Number,
    default: 0,
  },
  total2: {
    type: Number,
    default: 0,
  },
  patentList: {
    type: Array,
    default: () => [],
  },
  softwareList: {
    type: Array,
    default: () => [],
  },
});
const data = reactive({
  queryParams1: {
    pageNum: 1,
    pageSize: 10,
  },
  queryParams2: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams1, queryParams2 } = toRefs(data);

// 定义组件触发的事件
const emits = defineEmits(["update:visible"]);

// 关闭弹窗的方法
const closeDialog = () => {
  emits("update:visible", false);
};

const getList1 = () => {
  emits("pagination1", props.content.suppressSn, queryParams1.value);
};

const getList2 = () => {
  emits("pagination2", props.content.suppressSn, queryParams2.value);
};

// 处理遮罩层点击事件
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.9);

          :deep(.el-table__header th) {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          :deep(.el-table__body td) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }

          .bg-box-content-list {
            // padding: 0 0 10px 15px;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);

            span {
              font-weight: 600;
              color: rgba(255, 255, 255, 0.9);
              margin-right: 10px;
            }
          }
        }
      }
    }
  }
}
:deep(.pagination-container) {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump) {
  color: #f2f2f2;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination button:disabled) {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

:deep(.el-pager li) {
  background: #ffffff00 !important;
  color: #fff !important;

  &.is-active {
    color: #1890ff !important;
  }
}

// 添加多行文本省略样式
.ellipsis-text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 1.5;
}
</style>
