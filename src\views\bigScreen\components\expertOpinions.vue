<template>
  <div class="remengwenzhang-box">
    <div
      class="scroll-wrapper"
      ref="scrollWrapper"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @scroll="updateScrollbar"
    >
      <div class="scroll-content" ref="scrollContent">
        <div
          class="table_col"
          v-for="(item, index) in remengwenzhangList"
          :key="index"
          @click="openNewView(item)"
        >
          <el-tooltip
            class="item"
            effect="dark"
            :content="item.title"
            placement="top-start"
          >
            <div class="last-child">
              {{ item.title }}
            </div>
          </el-tooltip>
          <div class="table-main-content">
            {{ item.viewpoint }}
          </div>
          <div class="item-bottom">
            <el-row style="width: 100%;height: 100%;" justify="space-between">
              <el-col :span="7" class="text-left">
                <div class="item-bottom-text" v-if="item.publishTime">{{ parseTime(item.publishTime, "{y}-{m}-{d}") }}</div>
              </el-col>
              <el-col :span="10" class="text-center">
                <div class="item-bottom-text ellipsis" v-if="item.sourceName">{{ item.sourceName }}</div>
              </el-col>
              <el-col :span="7" class="text-right">
                <div class="item-bottom-text ellipsis" v-if="item.expertsName">{{ item.expertsName }}</div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { viewpointList } from "@/api/bigScreen/index";

const { proxy } = getCurrentInstance();

const props = defineProps({
  sccenId: {
    type: Number,
    default: 1,
  },
});

const emits = defineEmits(["openArticleDetail"]);

// 定义响应式数据
const remengwenzhangList = ref([]);
const scrollTimer = ref(null);
const isHovered = ref(false);
const scrollStep = ref(1);
const scrollWrapper = ref(null);
const scrollContent = ref(null);

const init = async () => {
  try {
    const res = await viewpointList({
      pageNum: 1,
      pageSize: 50,
      projectSn: "1",
      screenSn: props.sccenId,
      columnSn: "1",
    });
    remengwenzhangList.value = res.rows;
    proxy.$nextTick(() => {
      startScroll();
    });
  } catch (error) {
    console.error("获取热门文章列表失败:", error);
  }
};

const startScroll = () => {
  clearScrollTimer();
  if (!scrollWrapper.value || !scrollContent.value) return;

  scrollTimer.value = setInterval(() => {
    if (isHovered.value) return;

    if (
      scrollWrapper.value.scrollTop >=
      scrollContent.value.scrollHeight - scrollWrapper.value.clientHeight
    ) {
      scrollWrapper.value.scrollTop = 0;
    } else {
      scrollWrapper.value.scrollTop += scrollStep.value;
    }
    updateScrollbar();
  }, 30);
};

const clearScrollTimer = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value);
    scrollTimer.value = null;
  }
};

const handleMouseEnter = () => {
  isHovered.value = true;
};

const handleMouseLeave = () => {
  isHovered.value = false;
  startScroll();
};

const openNewView = (item) => {
  emits("openArticleDetail", { ...item, title: item.title });
};

const updateScrollbar = () => {
  if (!scrollWrapper.value) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollWrapper.value;
  const scrollPercent = clientHeight / scrollHeight;
  const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);
  const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;

  document.documentElement.style.setProperty(
    "--scrollbar-height",
    `${scrollbarHeight}px`
  );
  document.documentElement.style.setProperty(
    "--scrollbar-top",
    `${scrollbarTop}px`
  );
};

onMounted(() => {
  init();
  updateScrollbar();
});

onBeforeUnmount(() => {
  clearScrollTimer();
});
</script>

<style lang="scss" scoped>
.remengwenzhang-box {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow: hidden;
  position: relative;

  .scroll-wrapper {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    position: relative;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &::after {
    content: "";
    position: absolute;
    top: 20px;
    right: 0;
    height: calc(100% - 40px);
    width: 6px;
    background: rgba(16, 216, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
  }

  &:hover {
    &::after,
    .scroll-bar {
      opacity: 1;
    }
  }

  .table_col {
    width: 100%;
    height: 186px;
    background: url("@/assets/bigScreen/expertOpinionsBg.png") no-repeat 0px 0px !important;
    background-size: 100% 100% !important;
    padding: 10px 15px 10px;
    font-size: 14px;
    margin-bottom: 10px;
    cursor: pointer;

    .last-child {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 16px;
      font-weight: bold;
      color: #e6f7ff;
      font-style: normal;
    }

    .table-main-content {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      text-indent: 2em;
      line-height: 2;
      padding-top: 20px;
      height: 105px;
    }

    .item-bottom {
      margin-top: 8px;
      display: flex;
      font-family: "pingFangMedium";
      font-weight: 300;
      font-size: 14px;
      color: #e6f7ff;
      font-style: normal;
      justify-content: space-between;

      .text-left {
        text-align: left;
      }
      
      .text-center {
        text-align: center;
      }
      
      .text-right {
        text-align: right;
      }

      .item-bottom-text {
        min-width: 110px;
        width: fit-content;
        height: 30px;
        padding: 5px;
        background: rgba(47, 188, 254, 0.2);
        border-radius: 2px 2px 2px 2px;
        text-align: center;
        display: inline-block;
        
        &.ellipsis {
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
