<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :style="{ width: props.width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">美国提案分布情况</div>
          <div class="bg-box-content">
            <usaMap style="width: 530px; height: 284px"></usaMap>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">与中国相关</div>
          <div class="bg-box-content">
            <el-descriptions
              style="width: 100%; margin-bottom: 20px"
              direction="vertical"
              :column="5"
              border
              v-for="item in list2"
              :key="item.proposalsSn"
            >
              <el-descriptions-item label="提案时间" :span="1" width="150">{{
                item.publishTime
              }}</el-descriptions-item>
              <el-descriptions-item label="提案名称" :span="1" width="500">
                <div
                  style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                  @click="openpOlicyRiskContent(item.proposalsSn)"
                >
                  {{ item.proposalsTitle }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item
                label="提案人员"
                :span="1"
                class-name="experts"
                width="250"
              >
                <div class="descriptions-item-content">
                  <div
                    v-for="person in item.experts"
                    :key="person.expertsSn"
                    style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                    @click="openExpertsContent(person.expertsSn)"
                  >
                    {{ person.proposalsExperts }}
                  </div>
                </div>
              </el-descriptions-item>
              <el-descriptions-item
                label="所属党派"
                :span="1"
                class-name="experts"
              >
                <div class="descriptions-item-content">
                  <div v-for="group in item.experts" :key="group.expertsSn">
                    {{ group.belongToGroup }}
                  </div>
                </div>
              </el-descriptions-item>
              <el-descriptions-item
                label="所属州"
                :span="1"
                class-name="experts"
              >
                <div class="descriptions-item-content">
                  <div v-for="area in item.experts" :key="area.expertsSn">
                    {{ area.belongToArea }}
                  </div>
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="法案摘要" :span="5">
                <span v-html="item.summary"></span>
              </el-descriptions-item>
              <el-descriptions-item label="对中国的主要影响" :span="5">
                <span v-html="item.proposalsEffect"></span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">美国国会提案</div>
          <div class="bg-box-content">
            <el-table
              :data="processedTableData"
              border
              style="width: 100%"
              :span-method="calculateRowspan"
            >
              <el-table-column
                prop="time"
                label="提案时间"
                align="center"
                width="150"
              />
              <el-table-column
                prop="title"
                label="提案名称"
                align="center"
                width="500"
              >
                <template #default="scope">
                  <div
                    @click="openpOlicyRiskContent(scope.row.id)"
                    style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                  >
                    {{ scope.row.title }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="people.proposalsExperts"
                label="提案人员"
                align="center"
                width="250"
              >
                <template #default="scope">
                  <div
                    style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                    @click="openExpertsContent(scope.row.people.expertsSn)"
                  >
                    {{ scope.row.people.proposalsExperts }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="people.belongToGroup"
                label="所属党派"
                align="center"
              />
              <el-table-column
                prop="people.belongToArea"
                label="所属州"
                align="center"
              />
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <policyRiskContent
    :tianDetail="tianDetail"
    v-model:visible="policyRiskContentVisible"
    @openExpertsContent="openExpertsContent"
    ref="policyRiskContentRef"
  >
  </policyRiskContent>
  <policyRiskExperts
    :tianExpertsDetail="tianExpertsDetail"
    v-model:visible="policyRiskExpertsVisible"
    @openpOlicyRiskContent="openpOlicyRiskContent"
    ref="policyRiskExpertsRef"
  >
  </policyRiskExperts>
</template>

<script setup>
import {
  defineProps,
  defineEmits,
  computed,
  watch,
  ref,
  reactive,
  toRefs,
} from "vue";
import usaMap from "../components/usaMap";
import policyRiskContent from "./policyRiskContent";
import policyRiskExperts from "./policyRiskExperts";
import { proposalsDetail, proposalsExpertDetail } from "@/api/bigScreen/index";

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "自定义弹窗",
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1280,
  },
  list1: {
    type: Array,
    default: () => [],
  },
  list2: {
    type: Array,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
});
const policyRiskContentVisible = ref(false);
const policyRiskExpertsVisible = ref(false);
const tianDetail = ref({});
const tianExpertsDetail = ref({});

// 添加对组件实例的引用
const policyRiskContentRef = ref(null);
const policyRiskExpertsRef = ref(null);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);

// 用于存储合并信息
const mergeInfo = [];

// 处理表格数据
const processedTableData = computed(() => {
  const data = props.list1.flatMap((item) =>
    item.experts.map((person) => ({
      id: item.proposalsSn,
      time: item.publishTime,
      title: item.proposalsTitle,
      people: person,
    }))
  );

  // 清空合并信息数组
  mergeInfo.length = 0;

  for (let i = 0; i < data.length; i++) {
    let rowspan = 1;
    // 从当前行的下一行开始，查找 time 和 title 都相同的行
    for (let j = i + 1; j < data.length; j++) {
      if (data[j].time === data[i].time && data[j].title === data[i].title) {
        rowspan++;
      } else {
        break;
      }
    }
    // 存储当前行的合并行数
    for (let k = 0; k < rowspan; k++) {
      mergeInfo.push(k === 0 ? rowspan : 0);
    }
    i += rowspan - 1;
  }

  return data;
});

// 计算合并行数
const calculateRowspan = ({ row, column, rowIndex, columnIndex }) => {
  if (column.property === "time" || column.property === "title") {
    const rowspan = mergeInfo[rowIndex];
    return {
      rowspan: rowspan,
      colspan: 1,
    };
  }
  return {
    rowspan: 1,
    colspan: 1,
  };
};

// 定义组件触发的事件
const emits = defineEmits(["update:visible", "pagination"]);

// 关闭弹窗的方法
const closeDialog = () => {
  emits("update:visible", false);
};

const getList = () => {
  emits("pagination", queryParams.value);
};

// 处理遮罩层点击事件
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};

const openpOlicyRiskContent = (id) => {
  proposalsDetail({
    proposalsSn: id,
  }).then((res) => {
    tianDetail.value = res.data;

    // 如果内容组件已显示，则提升到前面
    if (policyRiskContentVisible.value) {
      policyRiskContentRef.value?.bringToFront();
    } else {
      policyRiskContentVisible.value = true;
    }
  });
};

const openExpertsContent = (id) => {
  proposalsExpertDetail({
    expertsSn: id,
  }).then((res) => {
    tianExpertsDetail.value = res.data;

    // 如果专家组件已显示，则提升到前面
    if (policyRiskExpertsVisible.value) {
      policyRiskExpertsRef.value?.bringToFront();
    } else {
      policyRiskExpertsVisible.value = true;
    }
  });
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;

          :deep(.el-table__header th) {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          :deep(.el-table__body td) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }
        }

        :deep(.el-descriptions__label) {
          background-color: #1f3850;
          color: rgba(255, 255, 255);
          font-size: 16px;
          text-align: center;
        }

        :deep(.el-descriptions__content) {
          background-color: #1d3046;
          color: rgba(255, 255, 255, 0.9);
          font-size: 14px;
          text-align: center;
        }

        :deep(.experts) {
          padding: 0;
          .descriptions-item-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            & > div {
              width: 100%;
              height: 40px;
              line-height: 40px;
              border-bottom: 1px solid #ffffff;
            }
            & > div:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }
}
:deep(.pagination-container) {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;
  width: 100%;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump) {
  color: #f2f2f2;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination button:disabled) {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

:deep(.el-pager li) {
  background: #ffffff00 !important;
  color: #fff !important;

  &.is-active {
    color: #1890ff !important;
  }
}
</style>
