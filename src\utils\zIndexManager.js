// z-index 管理模块

// 基础 z-index 值
const baseZIndex = 1000;

// 全局 z-index 计数器
let globalZIndex = baseZIndex;

/**
 * 获取新的 z-index 值
 * @returns {number} 递增后的 z-index 值
 */
export const getNewZIndex = () => {
  return ++globalZIndex;
};

/**
 * 重置 z-index 计数器
 */
export const resetZIndex = () => {
  globalZIndex = baseZIndex;
};

/**
 * 获取当前基础 z-index
 * @returns {number} 基础 z-index 值
 */
export const getBaseZIndex = () => {
  return baseZIndex;
}; 