<template>
  <div class="img-all" v-if="props.guanjianType == 1" style="padding-top: 15px">
    <div
      class="img-info"
      v-for="(item, index) in allImgList"
      :key="index"
      @click="openView(item)"
    >
      <div class="img">
        <img :src="baseUrl + item.cover" alt="" />
      </div>
    </div>
  </div>
  <div class="img-all experts-container" v-else>
    <div
      class="img-info1"
      v-for="(item, index) in allImgList1"
      :key="index"
      @click="openView(item)"
    >
      <div class="img1">
        <img :src="baseUrl + item.avatar" alt="" />
        <div class="info">
          <div class="name">{{ item.expertsName }}</div>
          <div class="unit">{{ item.expertsUnit }}</div>
          <div class="profile" v-html="item.profile"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { enterpriseList, expertsList } from "@/api/bigScreen/index";
import { ref, onMounted, nextTick } from "vue";

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const props = defineProps({
  screenType: {
    type: Number,
    default: 1,
  },
  guanjianType: {
    type: Number,
    default: 1,
  },
});
const emits = defineEmits(["openKeyDetail"]);
const allImgList = ref([]);
const allImgList1 = ref([]);

onMounted(() => {
  init();
});

const init = () => {
  enterpriseList({
    pageNum: 1,
    pageSize: 10,
    projectSn: "1",
    screenSn: props.screenType,
    columnSn: "1",
  }).then((response) => {
    allImgList.value = response.rows;
  });
  expertsList({
    pageNum: 1,
    pageSize: 6,
    projectSn: "1",
    screenSn: props.screenType,
    columnSn: "1",
  }).then((response) => {
    allImgList1.value = response.rows;
  });
};

const openView = async (item) => {
  console.log(item);
  emits("openKeyDetail", { ...item });
};
</script>

<style scoped lang="scss">
.img-all {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 0px;

  &.experts-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 10px 15px 0;
    align-content: space-between;
    height: 100%;
    justify-content: space-between;
  }

  .img-info {
    cursor: pointer;

    .img {
      width: 144px;
      height: 95px;
      background: #fff;
      vertical-align: middle;
      line-height: 160px;
      margin: 0px 5px;
      border-radius: 8px;

      img {
        width: 100%;
        height: 100%;
        padding: 4px;
        display: inline-block;
        border-radius: 8px;
        object-fit: contain;
      }
    }
  }

  .img-info1 {
    cursor: pointer;
    width: calc((100% - 30px) / 3);
    height: calc(50% - 7.5px);
    box-sizing: border-box;

    .img1 {
      display: flex;
      height: 100%;
      background: #082753;
      border-radius: 8px;
      overflow: hidden;

      img {
        width: 100px;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }

      .info {
        flex: 1;
        height: 100%;
        padding: 10px;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .name {
          font-size: 16px;
          color: #0190d3;
          font-weight: 600;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          white-space: normal;
          word-break: break-all;
          margin-bottom: 2px;
        }
        .unit {
          font-size: 14px;
          color: #fff;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          white-space: normal;
          word-break: break-all;
          line-height: 20px;
          margin-bottom: 3px;
        }
        .profile {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          white-space: normal;
          word-break: break-all;
          line-height: 20px;
          flex: 1;
          max-height: 60px; /* 3行文本的最大高度 */
        }
      }
    }
  }
}
</style>
