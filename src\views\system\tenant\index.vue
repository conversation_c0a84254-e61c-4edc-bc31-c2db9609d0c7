<template>
  <div class="app-container">
    <el-form style="margin-bottom: 10px; padding-top: 20px; border: 2px solid #eeeeee;" :model="queryParams"
             ref="queryForm" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="租户名称" prop="tenantName">
        <el-input style="width: 240px" v-model="queryParams.tenantName" placeholder="请输入租户名称" clearable
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="电话" prop="phone">
        <el-input style="width: 240px" v-model="queryParams.phone" placeholder="请输入电话" clearable
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select style="width: 240px" v-model="queryParams.status" placeholder="用户状态" clearable>
          <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker style="width: 240px" v-model="queryParams.dateRange" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['article:tenant:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                   v-hasPermi="['article:tenant:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                   v-hasPermi="['article:tenant:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
                   v-hasPermi="['article:tenant:export']">导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tenantList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="租户ID" align="center" prop="tenantId" width="80"/>
      <el-table-column label="租户名称" prop="tenantName" min-width="160" show-overflow-tooltip/>
      <el-table-column label="地址" prop="address" width="150" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.address }}
        </template>
      </el-table-column>
      <el-table-column label="邮箱" prop="email" width="160" show-overflow-tooltip/>
      <el-table-column label="电话" prop="phone" width="160" show-overflow-tooltip/>
      <el-table-column label="是否激活" align="center" prop="status" width="100">
        <template #default="scope">
          <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                     @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="有效截止时间" prop="activeTime" width="140">
        <template #default="scope">
          <span>{{ parseTime(scope.row.activeTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="140">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="200px">
        <template #default="scope">
          <el-button link icon="Edit" type="primary" @click="handleUpdate(scope.row)"
                     v-hasPermi="['article:tenant:edit']">修改
          </el-button>
          <el-button link icon="Delete" type="primary" @click="handleDelete(scope.row)"
                     v-hasPermi="['article:tenant:remove']">删除
          </el-button>
          <el-button link icon="Edit" type="primary" @click="openDistribution(scope.row)"
                     v-hasPermi="['article:tenant:edit']">分配数据源
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 分配任务对话框 -->
    <el-dialog title="分配数据源" v-model="dialogVisible" width="1200px" append-to-body :before-close="taskClose"
               :close-on-click-modal="false" top="5vh">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <h3>租户</h3>
            <el-input v-model="dialogForm.tenantName" placeholder="请输入租户名称" disabled/>
            <h3>选择数据源</h3>
            <el-form :model="taskQueryParams" ref="taskQueryForm" :inline="true" label-width="68px">
              <el-form-item label="类型" prop="sourceType">
                <el-select v-model="taskQueryParams.sourceType" placeholder="请选择类型" clearable style="width: 180px;">
                  <el-option v-for="(item, index) in sourceTypeList" :key="index" :label="item.name"
                             :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="url" prop="sourceUrl">
                <el-input v-model="taskQueryParams.sourceUrl" placeholder="url" clearable style="width: 180px;"/>
              </el-form-item>
              <el-form-item label="数据源" prop="sourceName">
                <el-input v-model="taskQueryParams.sourceName" placeholder="数据源" clearable style="width: 180px;"/>
              </el-form-item>
              <el-form-item label="别名" prop="sourceAliasName">
                <el-input v-model="taskQueryParams.sourceAliasName" placeholder="别名" clearable style="width: 180px;"/>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="taskListQuery">查询</el-button>
                <el-button type="primary" @click="taskListResart">重置</el-button>
                <el-button @click="batchAllocation(true)">批量分配</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="taskLoading" :data="taskList" ref="taskLable" @selection-change="taskSelection">
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column label="类型" prop="sourceTypeName" width="120">
              </el-table-column>
              <el-table-column label="数据源" prop="sourceName" show-overflow-tooltip/>
              <el-table-column label="别名" prop="sourceName" show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="URL" prop="sourceUrl" show-overflow-tooltip>
                <template #default="{ row }">
                  <a href="" :href="row.sourceUrl" target="_blank" rel="noopener noreferrer" class="link-a">
                    {{ row.sourceUrl }}
                  </a>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="taskTotal > 0" :total="taskTotal" :page.sync="taskQueryParams.pageNum"
                        :limit.sync="taskQueryParams.pageSize" @pagination="taskListQuery"
                        :layout="'total, prev, pager, next'"/>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple">
            <div style="height: 79px;"></div>
            <h3>已分配数据源</h3>
            <el-form :model="taskQueryParams1" ref="taskQueryForm1" :inline="true" label-width="68px">
              <el-form-item label="类型" prop="sourceType">
                <el-select v-model="taskQueryParams1.sourceType" placeholder="请选择类型" clearable style="width: 180px;">
                  <el-option v-for="(item, index) in sourceTypeList" :key="index" :label="item.name"
                             :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="url" prop="sourceUrl">
                <el-input v-model="taskQueryParams1.sourceUrl" placeholder="url" clearable style="width: 180px;"/>
              </el-form-item>
              <el-form-item label="数据源" prop="sourceName">
                <el-input v-model="taskQueryParams1.sourceName" placeholder="数据源" clearable style="width: 180px;"/>
              </el-form-item>
              <el-form-item label="别名" prop="sourceAliasName">
                <el-input v-model="taskQueryParams1.sourceAliasName" placeholder="别名" clearable style="width: 180px;"/>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="taskListQuery1">查询</el-button>
                <el-button type="primary" @click="taskListResart1">重置</el-button>
                <el-button @click="batchAllocation(false)">批量取消</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="taskLoading1" :data="taskList1" ref="taskLable1"
                      @selection-change="assignedTaskSelection">
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column label="类型" prop="sourceTypeName" width="120">
              </el-table-column>
              <el-table-column label="数据源" prop="sourceName" show-overflow-tooltip/>
              <el-table-column label="别名" prop="sourceName" show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="URL" prop="sourceUrl" show-overflow-tooltip>
                <template #default="{ row }">
                  <a href="" :href="row.sourceUrl" target="_blank" rel="noopener noreferrer" class="link-a">
                    {{ row.sourceUrl }}
                  </a>
                </template>
              </el-table-column>
              <el-table-column label="权重" prop="weight" show-overflow-tooltip>
                <template #default="{ row }">
                  <el-input type="number" v-model="row.weight" placeholder="权重" @change="changeWeight(row)"/>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="taskTotal1 > 0" :total="taskTotal1" :page.sync="taskQueryParams1.pageNum"
                        :limit.sync="taskQueryParams1.pageSize" @pagination="taskListQuery1"
                        :layout="'total, prev, pager, next'"/>
          </div>
        </el-col>
      </el-row>
    </el-dialog>

    <!-- 添加或修改租户对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-row class="mb8">
          <el-col :span="12">
            <el-form-item label="租户名称" prop="tenantName">
              <el-input v-model="form.tenantName" placeholder="请输入租户名称"/>
            </el-form-item>
            <el-form-item v-if="title == '添加租户'" label="登录账号" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入登录账号"/>
            </el-form-item>
            <el-form-item v-if="title == '添加租户'" label="密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入密码" type="password" maxlength="20" show-password/>
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-cascader style="width: 100%;" v-model="form.addressRemark" :options="addressOptions"
                           :props="{ checkStrictly: true, value: 'id' }" clearable></el-cascader>
              <el-input style="margin-top: 10px;width: 100%;" v-model="form.address" type="textarea"
                        placeholder="请输入详细地址"/>
            </el-form-item>
            <el-form-item label="电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入电话"/>
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱"/>
            </el-form-item>
            <el-form-item label="有效截止时间" prop="activeTime">
              <el-date-picker style="width: 100%;" clearable v-model="form.activeTime" type="date"
                              value-format="YYYY-MM-DD" placeholder="请选择有效截止时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="是否激活" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{
                    dict.label
                  }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="form.systemName" placeholder="请输入系统名称"/>
            </el-form-item>
            <el-form-item label="首页路由" prop="defaultHome">
              <el-input v-model="form.defaultHome" placeholder="请输入首页路由,不设置默认跳转/index"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单权限">
              <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
              <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
              <el-checkbox v-model="form.menuCheckStrictly"
                           @change="handleCheckedTreeConnect($event, 'menu')">父子联动
              </el-checkbox>
              <el-tree class="tree-border" :data="menuOptions" show-checkbox ref="menuRef" node-key="id"
                       :check-strictly="!form.menuCheckStrictly" empty-text="加载中，请稍候" :props="defaultProps"></el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {nextTick, onMounted, reactive, ref} from 'vue';
import {
  addTenant,
  delTenant,
  getDefaultMenu,
  getListClassify,
  getTenant,
  getTenantAssignData,
  listTenant,
  regionTree,
  removeTenantSource,
  updateTenant,
  updateTenantEditStatus,
  updateTenantSource,
  updateWeight
} from "@/api/system/tenant";
import {treeselect as menuTreeselect} from "@/api/system/menu";
import {encrypt} from '@/utils/jsencrypt.js'
import {ElMessage, ElMessageBox} from 'element-plus';

const {proxy} = getCurrentInstance();
const {sys_normal_disable} = proxy.useDict("sys_normal_disable");

// 引用
const formRef = ref(null);
const menuRef = ref(null);
const taskQueryForm = ref(null);
const taskQueryForm1 = ref(null);

// 遮罩层
const loading = ref(true);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 租户表格数据
const tenantList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  tenantName: null,
  phone: null,
  status: null,
  dateRange: null,
});
// 表单参数
const form = reactive({
  tenantName: null,
  userName: null,
  password: null,
  address: null,
  addressRemark: [],
  phone: null,
  email: null,
  activeTime: null,
  status: "0",
  systemName: '',
  tenantMenuList: [],
  menuExpand: false,
  menuNodeAll: true,
  menuCheckStrictly: true,
});
// 表单校验
const rules = reactive({
  tenantName: [
    {required: true, message: "租户名称不能为空", trigger: "blur"}
  ],
  userName: [
    {required: true, message: '登录账号不能为空', trigger: 'blur'}
  ],
  password: [
    {
      required: true,
      validator: (rule, value, callback) => {
        let reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).+$/;
        if (!value) {
          callback(new Error('密码不能为空'));
        } else if (value.length < 8 || value.length > 20) {
          callback(new Error('请输入8-20位的密码'));
        } else if (!reg.test(value)) {
          callback(new Error('请输入正确格式的密码,密码包含数字、大小写字母、特殊符号'));
        } else {
          callback();
        }
      }, trigger: 'blur'
    }
  ],
  phone: [
    {required: true, message: "电话不能为空", trigger: "blur"},
    {pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur'}
  ],
  email: [
    {required: true, message: "邮箱不能为空", trigger: "blur"},
    {type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change']}
  ],
  activeTime: [
    {required: true, message: "有效截止时间不能为空", trigger: "blur"}
  ],
});
// 部门列表
const menuOptions = ref([]);
const defaultProps = reactive({
  children: "children",
  label: "label"
});
const menuExpand = ref(false);
const menuNodeAll = ref(false);
const deptExpand = ref(true);
// 地区选择
const addressOptions = ref([]);
const initPassword = ref(null); //默认密码
const dialogVisible = ref(false); // 分配数据源弹窗
const dialogForm = reactive({ // 分配数据源数据
  tenantId: '',
  sourceSn: [],
});
const dialogRules = reactive({ // 分配数据源表单校验
  tenantId: [
    {required: true, message: "不能为空", trigger: "blur"}
  ]
});
const snOption = ref([]); // 数据源列表
const filterText = ref(''); // 数据源过滤词
const taskQueryParams = reactive({ // 任务查询参数
  pageNum: 1,
  pageSize: 10,
  sourceType: null,
  sourceAliasName: null,
  sourceName: null,
  sourceUrl: null,
});
const taskLoading = ref(false); // 任务loading
const taskList = ref([]); // 任务列表
const taskTotal = ref(0); // 任务总数
const taskSelectionList = ref([]); // 任务选择的数据
const taskQueryParams1 = reactive({ // 已分配任务查询参数
  pageNum: 1,
  pageSize: 10,
  sourceType: null,
  sourceAliasName: null,
  sourceName: null,
  sourceUrl: null,
});
const taskLoading1 = ref(false); // 已分配任务loading
const taskList1 = ref([]); // 已分配任务列表
const taskTotal1 = ref(0); // 已分配任务总数
const assignedTaskSelectionList = ref([]); // 已分配任务选择的数据
const sourceTypeList = ref([]); // 数据源分类

// 生命周期钩子
onMounted(() => {
  getMenuTreeselect();
  // getList()
  getRegionTree();
  getListClassify().then(res => {
    sourceTypeList.value = res.data;
  });
  // getConfigKey('sys.user.initPassword').then(response => {
  //   initPassword.value = response.msg;
  // });
});

// 方法定义
const getList = () => {
  loading.value = true;
  listTenant(proxy.addDateRange(queryParams, queryParams.dateRange)).then(response => {
    tenantList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

const getRegionTree = () => {
  regionTree().then(response => {
    addressOptions.value = response.data;
    getList();
  });
};

const cancel = () => {
  reset();
  open.value = false;
};

const reset = () => {
  if (menuRef.value) {
    menuRef.value.setCheckedKeys([]);
  }
  menuExpand.value = false;
  menuNodeAll.value = false;
  deptExpand.value = true;
  proxy.resetForm("formRef");
};

const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const resetQuery = () => {
  proxy.resetForm("queryForm");
  handleQuery();
};

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.tenantSn);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

const handleAdd = () => {
  open.value = true;
  getDefaultMenu().then(res => {
    menuRef.value.setCheckedKeys(res.data);
    if (initPassword.value) form.password = initPassword.value;
  });
  title.value = "添加租户";
};

const handleUpdate = (row) => {
  const tenantId = row.tenantSn || ids.value;
  getTenant(tenantId).then(response => {
    Object.assign(form, response.data);
    form.addressRemark = response.data.regionCode ? [Number(response.data.regionCode)] : [];
    form.menuCheckStrictly = true;
    open.value = true;
    setTimeout(() => {
      nextTick(() => {
        response.data.checkedKeys.map((v) => {
          menuRef.value.setChecked(v, true, false);
        });
      });
    }, 100);
    title.value = "修改租户";
  });
};

const submitForm = () => {
  proxy.$refs["formRef"].validate(async valid => {
    if (valid) {
      form.tenantMenuList = getMenuAllCheckedKeys();
      if (form.tenantMenuList && form.tenantMenuList.length !== 0) {
        let queryForm = JSON.parse(JSON.stringify(form))
        queryForm.password = encrypt(queryForm.password)
        queryForm.phone = encrypt(queryForm.phone)
        queryForm.email = encrypt(queryForm.email)
        queryForm.regionCode = queryForm.addressRemark ? queryForm.addressRemark[0] : '';
        if (!!queryForm.tenantId) {
          await updateTenant({...queryForm}).then(response => {
            ElMessage.success("修改成功");
            cancel();
            getList();
          });
        } else {
          await addTenant({...queryForm}).then(response => {
            ElMessage.success("新增成功");
            cancel();
            getList();
          })
        }
      } else {
        ElMessage.error('请选择菜单权限');
      }
    }
  });
};

const handleDelete = (row) => {
  let tenantIds = ids.value;
  if (row.tenantId) {
    tenantIds = [row.tenantId];
  }
  ElMessageBox.confirm('是否确认删除租户编号为"' + tenantIds + '"的数据项？').then(() => {
    return delTenant(tenantIds);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => {
  });
};

const handleExport = () => {
  proxy.download('system/tenant/export', {
    ...queryParams
  }, `tenant_${new Date().getTime()}.xlsx`);
};

const handleStatusChange = (row) => {
  let text = row.status === "0" ? "启用" : "停用";
  ElMessageBox.confirm('确认要"' + text + '""' + row.tenantName + '"角色吗？').then(() => {
    return updateTenantEditStatus({tenantId: row.tenantId, status: row.status});
  }).then(() => {
    ElMessage.success(text + "成功");
    getList();
  }).catch(() => {
    row.status = row.status === "0" ? "1" : "0";
  });
};

const getMenuTreeselect = () => {
  menuTreeselect().then(response => {
    menuOptions.value = response.data;
  });
};

const handleCheckedTreeExpand = (value, type) => {
  let treeList = menuOptions.value;
  for (let i = 0; i < treeList.length; i++) {
    menuRef.value.store.nodesMap[treeList[i].id].expanded = value;
  }
};

const handleCheckedTreeNodeAll = (value, type) => {
  menuRef.value.setCheckedNodes(value ? menuOptions.value : []);
};

const handleCheckedTreeConnect = (value, type) => {
  form.menuCheckStrictly = value ? true : false;
};

const getMenuAllCheckedKeys = () => {
  let checkedKeys = menuRef.value.getCheckedNodes();
  let halfCheckedKeys = menuRef.value.getHalfCheckedNodes();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  checkedKeys = checkedKeys.map(item => {
    return {menuId: item.id, menuName: item.label};
  });
  return checkedKeys;
};

const openDistribution = async (row) => {
  taskQueryParams.tenantId = taskQueryParams1.tenantId = row.tenantId;
  taskQueryParams.tenantName = taskQueryParams1.tenantName = row.tenantName;
  dialogVisible.value = true;
  taskListQuery();
  taskListQuery1();
};

const taskListQuery = () => {
  taskLoading.value = true;
  getTenantAssignData({...taskQueryParams, assignmentType: 0}).then(response => {
    taskList.value = response.rows;
    taskTotal.value = response.total;
    taskLoading.value = false;
  });
};

const taskListQuery1 = () => {
  taskLoading1.value = true;
  getTenantAssignData({...taskQueryParams1, assignmentType: 1}).then(response => {
    taskList1.value = response.rows;
    taskTotal1.value = response.total;
    taskLoading1.value = false;
  });
};

const taskListResart = () => {
  proxy.resetForm("taskQueryForm");
  taskListQuery();
};

const taskListResart1 = () => {
  proxy.resetForm("taskQueryForm1");
  taskListQuery1();
};

const taskClose = () => {
  proxy.resetForm("taskQueryForm");
  proxy.resetForm("taskQueryForm1");
  taskList.value = [];
  taskList1.value = [];
  taskTotal.value = 0;
  taskTotal1.value = 0;
  dialogVisible.value = false;
  getList();
};

const taskSelection = (row) => {
  taskSelectionList.value = row;
};

const assignedTaskSelection = (row) => {
  assignedTaskSelectionList.value = row;
};

const batchAllocation = (boo) => {
  if (boo) {
    if (taskSelectionList.value.length > 0) {
      const data = {
        tenantId: taskQueryParams.tenantId,
        sourceSn: taskSelectionList.value.map(item => item.sourceSn)
      };
      updateTenantSource(data).then(response => {
        ElMessage.success("批量分配成功");
        taskListQuery();
        taskListQuery1();
      });
    } else {
      ElMessage.error("请选择要分配的数据源");
    }
  } else {
    if (assignedTaskSelectionList.value.length > 0) {
      const data = {
        tenantId: taskQueryParams1.tenantId,
        sourceSn: assignedTaskSelectionList.value.map(item => item.sourceSn)
      };
      removeTenantSource(assignedTaskSelectionList.value.map(item => item.id)).then(response => {
        ElMessage.success("批量取消成功");
        taskListQuery();
        taskListQuery1();
      });
    } else {
      ElMessage.error("请选择要取消的数据源");
    }
  }
};

const changeWeight = (item) => {
  let formData = new FormData();
  formData.append('id', item.id);
  formData.append('weight', item.weight);
  updateWeight(formData).then(res => {
    ElMessage.success("更改成功");
  });
};
</script>
<style lang="scss" scoped>
.tree-border {
  max-height: calc(100vh - 400px);
  overflow: auto;
}

:deep.el-dialog__body {
  padding: 10px 10px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  // position: absolute;
  bottom: 50px;
  width: 100%;
}

:deep .el-transfer-panel {
  width: 348px;
  height: 600px;

  &__body {
    height: 600px !important;

    .el-transfer-panel__list {
      height: 500px !important;
    }
  }
}
</style>