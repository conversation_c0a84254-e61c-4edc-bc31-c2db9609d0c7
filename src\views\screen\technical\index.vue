<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="唯一标识" prop="technicalSn">
        <el-input v-model="queryParams.technicalSn" placeholder="请输入唯一标识" clearable />
      </el-form-item>
      <el-form-item label="技术名称" prop="technicalName">
        <el-input v-model="queryParams.technicalName" placeholder="请输入技术名称" clearable />
      </el-form-item>
      <el-form-item label="是否境外" prop="isForeign">
        <el-select v-model="queryParams.isForeign" placeholder="请选择是否是境外" clearable style="width:190px">
          <el-option label="境外" value="0"></el-option>
          <el-option label="国内" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="总篇数" prop="totalArticles">
        <el-input v-model="queryParams.totalArticles" placeholder="请输入总篇数" clearable />
      </el-form-item>
      <el-form-item label="总次数" prop="totalTimes">
        <el-input v-model="queryParams.totalTimes" placeholder="请输入总次数" clearable />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectSn">
        <el-input v-model="queryParams.projectSn" placeholder="请输入项目编码" clearable />
      </el-form-item>
      <el-form-item label="租户编码" prop="tenantSn">
        <el-input v-model="queryParams.tenantSn" placeholder="请输入租户编码" clearable />
      </el-form-item>
      <el-form-item label="屏幕编码" prop="screenSn">
        <el-input v-model="queryParams.screenSn" placeholder="请输入屏幕编码" clearable />
      </el-form-item>
      <el-form-item label="栏目编码" prop="columnSn">
        <el-input v-model="queryParams.columnSn" placeholder="请输入栏目编码" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['screen:technical:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:technical:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:technical:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['screen:technical:export']">导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          v-hasPermi="['screen:technical:add']">前沿热点技术导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImportTrend"
          v-hasPermi="['screen:technicalTrend:add']">内容趋势导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImportArticle"
          v-hasPermi="['screen:technicalArticle:add']">相关文章导入</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="technicalList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" prop="technicalSn" width="150" />
      <el-table-column label="技术名称" prop="technicalName" width="300" show-overflow-tooltip />
      <el-table-column label="是否是境外" align="center" prop="isForeign" width="150">
        <template #default="scope">
          {{ scope.row.isForeign == 0 ? '境外' : '国内' }}
        </template>
      </el-table-column>
      <el-table-column label="总篇数" align="center" prop="totalArticles" width="150" />
      <el-table-column label="总次数" align="center" prop="totalTimes" width="150" />
      <el-table-column label="技术简介" prop="summary" width="150" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status == '0' ? 'success' : 'danger'">{{
            scope.row.status == '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目编码" align="center" prop="projectSn" />
      <el-table-column label="租户编码" align="center" prop="tenantSn" show-overflow-tooltip/>
      <el-table-column label="屏幕编码" align="center" prop="screenSn" />
      <el-table-column label="栏目编码" align="center" prop="columnSn" />
      <el-table-column label="操作" align="center" fixed="right" min-width="320" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:technical:edit']">修改
          </el-button>
          <el-button link type="primary" icon="Edit" @click="handleTrend(scope.row)">内容趋势
          </el-button>
          <el-button link type="primary" icon="Edit" @click="handleArticle(scope.row)">相关文章
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:technical:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改前沿技术热点对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="technicalRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="唯一标识" prop="technicalSn">
          <el-input v-model="form.technicalSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="技术名称" prop="technicalName">
          <el-input v-model="form.technicalName" placeholder="请输入技术名称" />
        </el-form-item>
        <el-form-item label="是否是境外" prop="isForeign">
          <el-select v-model="form.isForeign" placeholder="请选择是否是境外">
            <el-option label="境外" value="0"></el-option>
            <el-option label="国内" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否是推荐" prop="screenRecommend">
          <el-radio-group v-model="form.screenRecommend">
            <el-radio :value="'0'">否</el-radio>
            <el-radio :value="'1'">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="总篇数" prop="totalArticles">
          <el-input v-model="form.totalArticles" placeholder="请输入总篇数" />
        </el-form-item>
        <el-form-item label="总次数" prop="totalTimes">
          <el-input v-model="form.totalTimes" placeholder="请输入总次数" />
        </el-form-item>
        <el-form-item label="技术简介" prop="summary">
          <el-input v-model="form.summary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-row>
          <el-col :span="12" v-if="userStore.roles.includes('admin')">
            <el-form-item label="租户编码" prop="tenantSn">
              <el-input v-model="form.tenantSn" placeholder="请输入租户编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="projectSn">
              <el-input v-model="form.projectSn" placeholder="请输入项目编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕编码" prop="screenSn">
              <el-input v-model="form.screenSn" placeholder="请输入屏幕编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="栏目编码" prop="columnSn">
              <el-input v-model="form.columnSn" placeholder="请输入栏目编码" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <technicalArticle v-model:visible="technicalArticleVisible" :sn="technicalArticleSn"
      :visibleTitle="technicalArticleTitle" @closeArticle="closeArticle">
    </technicalArticle>
    <technicalTrend v-model:visible="technicalTrendVisible" :sn="technicalTrendSn" :visibleTitle="technicalTrendTitle"
      @closeTrend="closeTrend">
    </technicalTrend>
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Technical">
import { getToken } from "@/utils/auth";
import { addTechnical, delTechnical, getTechnical, listTechnical, updateTechnical } from "@/api/screen/technical";
import technicalArticle from '../secondLevel/technicalArticle/index.vue'
import technicalTrend from '../secondLevel/technicalTrend/index.vue'
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();

const technicalList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    technicalSn: null,
    technicalName: null,
    isForeign: null,
    totalArticles: null,
    totalTimes: null,
    summary: null,
    orderNum: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    projectSn: null,
    tenantSn: null,
    screenSn: null,
    columnSn: null
  },
  rules: {
    projectSn: [
      { required: true, message: "项目编码不能为空", trigger: "blur" }
    ],
    // tenantSn: [
    //   { required: true, message: "租户编码不能为空", trigger: "blur" }
    // ],
    screenSn: [
      { required: true, message: "屏幕编码不能为空", trigger: "blur" }
    ],
    columnSn: [
      { required: true, message: "栏目编码不能为空", trigger: "blur" }
    ]
  },
  technicalArticleVisible: false,
  technicalArticleSn: null,
  technicalArticleTitle: '',
  technicalTrendVisible: false,
  technicalTrendSn: null,
  technicalTrendTitle: '',
});

const { queryParams, form, rules, technicalArticleVisible, technicalArticleSn, technicalArticleTitle, technicalTrendVisible, technicalTrendSn, technicalTrendTitle } = toRefs(data);

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  businessType: null,
  headers: { Authorization: "Bearer " + getToken() },
  url: ''
});

/** 查询前沿技术热点列表 */
function getList() {
  loading.value = true;
  listTechnical(queryParams.value).then(response => {
    technicalList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    technicalSn: null,
    technicalName: null,
    isForeign: null,
    totalArticles: null,
    totalTimes: null,
    summary: null,
    orderNum: null,
    status: null,
    delFlag: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    projectSn: null,
    tenantSn: null,
    screenSn: null,
    columnSn: null
  };
  proxy.resetForm("technicalRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加前沿技术热点";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getTechnical(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改前沿技术热点";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["technicalRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateTechnical(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTechnical(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  _ids = _ids.flatMap(item => {
    const found = technicalList.value.find(row => row.id == item)
    return found?.technicalSn ? [found.technicalSn] : []
  })
  proxy.$modal.confirm('是否确认删除前沿技术热点编号为"' + _ids + '"的数据项？').then(function () {
    return delTechnical(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/technical/export', {
    ...queryParams.value
  }, `technical_${new Date().getTime()}.xlsx`)
}

const handleArticle = (row) => {
  technicalArticleSn.value = row.technicalSn
  technicalArticleTitle.value = row.technicalName
  technicalArticleVisible.value = true
}

const closeArticle = () => {
  technicalArticleVisible.value = false
}
const handleTrend = (row) => {
  technicalTrendSn.value = row.technicalSn
  technicalTrendTitle.value = row.technicalName
  technicalTrendVisible.value = true
}

const closeTrend = () => {
  technicalTrendVisible.value = false
}

function handleImport() {
  upload.title = "前沿热点技术导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/technical/excelImport";
  upload.businessType = 100;
  upload.open = true;
};

function handleImportTrend() {
  upload.title = "内容趋势导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/technicalTrend/excelImport";
  upload.businessType = 101;
  upload.open = true;
};

function handleImportArticle() {
  upload.title = "相关文章导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/technicalArticle/excelImport";
  upload.businessType = 102;
  upload.open = true;
};

function importTemplate() {
  proxy.download("xty-screen/technical/downloadTemplate?businessType=" + upload.businessType, {
  }, `${upload.title}模版.xlsx`);
};

const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};

function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

getList();
</script>
