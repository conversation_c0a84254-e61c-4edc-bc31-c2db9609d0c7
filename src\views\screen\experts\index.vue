<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="90px"
      class="queryForm"
    >
      <el-row>
        <el-col :span="5">
          <el-form-item label="关键人编码" prop="expertsSn">
            <el-input
              v-model="queryParams.expertsSn"
              placeholder="请输入关键人编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="姓名" prop="expertsName">
            <el-input
              v-model="queryParams.expertsName"
              placeholder="请输入姓名"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="英文名" prop="expertsNameEn">
            <el-input
              v-model="queryParams.expertsNameEn"
              placeholder="请输入英文名"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="职务/头衔" prop="expertsPosition">
            <el-input
              v-model="queryParams.expertsPosition"
              placeholder="请输入职务/头衔"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="单位" prop="expertsUnit">
            <el-input
              v-model="queryParams.expertsUnit"
              placeholder="请输入单位"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="职称 " prop="expertsTitle">
            <el-input
              v-model="queryParams.expertsTitle"
              placeholder="请输入职称"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="租户编码" prop="tenantSn">
            <el-input
              v-model="queryParams.tenantSn"
              placeholder="请输入租户编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="20"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="项目编码" prop="projectSn">
            <el-input
              v-model="queryParams.projectSn"
              placeholder="请输入项目编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="20"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="屏幕编码" prop="screenSn">
            <el-input
              v-model="queryParams.screenSn"
              placeholder="请输入屏幕编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="20"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="栏目编码" prop="columnSn">
            <el-input
              v-model="queryParams.columnSn"
              placeholder="请输入栏目编码"
              clearable
              @keyup.enter="handleQuery"
              :maxlength="20"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['screen:experts:add']"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['screen:experts:edit']"
          >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['screen:experts:remove']"
          >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['screen:experts:export']"
          >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['screen:experts:add']"
          >导入关键人</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImportTrend"
          v-hasPermi="['screen:expertsTrend:add']"
          >导入关键人趋势</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImportArticle"
          v-hasPermi="['screen:expertsArticle:add']"
          >导入关键人文章</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="expertsList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 370px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="关键人编码"
        align="center"
        prop="expertsSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.expertsSn)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.expertsSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="姓名"
        align="center"
        prop="expertsName"
        width="150"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.expertsName)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.expertsName }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="英文名"
        align="center"
        prop="expertsNameEn"
        width="150"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.expertsNameEn)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.expertsNameEn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="职务/头衔"
        align="center"
        prop="expertsPosition"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.expertsPosition)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.expertsPosition }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="单位"
        align="center"
        prop="expertsUnit"
        width="150"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.expertsUnit)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.expertsUnit }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="职称"
        align="center"
        prop="expertsTitle"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.expertsTitle)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.expertsTitle }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="头像" align="center" prop="avatar" width="150">
        <template #default="scope">
          <img
            :src="baseUrl + scope.row.avatar"
            style="width: 100px; height: 120px; object-fit: cover"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishTime"
        width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="关键词"
        align="center"
        prop="keywords"
        width="200"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.keywords)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.keywords }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="研究领域"
        align="center"
        prop="researchArea"
        width="200"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.researchArea)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.researchArea }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="荣誉/成就"
        align="center"
        prop="honorsAndAchievements"
        width="200"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.honorsAndAchievements)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">
              {{ scope.row.honorsAndAchievements }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="专家简介"
        align="center"
        prop="profile"
        width="300"
      /> -->
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === "0" ? "正常" : "停用"
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="租户编码"
        align="center"
        prop="tenantSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.tenantSn)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.tenantSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="项目编码"
        align="center"
        prop="projectSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.projectSn)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.projectSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="屏幕编码"
        align="center"
        prop="screenSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.screenSn)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.screenSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="栏目编码"
        align="center"
        prop="columnSn"
        width="100"
      >
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.columnSn)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.columnSn }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" width="150">
        <template #default="scope">
          <el-tooltip
            :content="String(scope.row.remark)"
            placement="top"
            :show-after="200"
            popper-class="custom-tooltip"
          >
            <div class="cell-content">{{ scope.row.remark }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <div class="btns">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['screen:experts:edit']"
              >修改
            </el-button>
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['screen:experts:remove']"
              >删除
            </el-button>
            <el-button
              link
              type="primary"
              icon="DataAnalysis"
              @click="handleTrend(scope.row)"
              >内容趋势</el-button
            >
            <el-button
              link
              type="primary"
              icon="Document"
              @click="handleRelatedContent(scope.row)"
              >相关内容</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改关键人对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="expertsRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item
          label="关键人编码"
          prop="expertsSn"
          v-if="title == '修改关键人'"
        >
          <el-input
            v-model="form.expertsSn"
            placeholder="请输入关键人编码"
            disabled
          />
        </el-form-item>
        <el-form-item label="姓名" prop="expertsName">
          <el-input
            v-model="form.expertsName"
            placeholder="请输入姓名"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="英文名" prop="expertsNameEn">
          <el-input
            v-model="form.expertsNameEn"
            placeholder="请输入英文名"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="职务/头衔" prop="expertsPosition">
          <el-input
            v-model="form.expertsPosition"
            placeholder="请输入职务/头衔"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="单位" prop="expertsUnit">
          <el-input
            v-model="form.expertsUnit"
            placeholder="请输入单位"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="职称" prop="expertsTitle">
          <el-input
            v-model="form.expertsTitle"
            placeholder="请输入职称"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker
            clearable
            v-model="form.publishTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择发布时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <image-upload
            v-model="form.avatar"
            :limit="1"
            :custom-data="{ businessType: 1 }"
            @update:modelValue="handleCoverUpdate"
          />
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input
            v-model="form.keywords"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="研究领域" prop="researchArea">
          <el-input
            v-model="form.researchArea"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="荣誉/成就" prop="honorsAndAchievements">
          <el-input
            v-model="form.honorsAndAchievements"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="专家简介" prop="profile">
          <editor
            v-model="form.profile"
            :min-height="192"
            :key="profileEditorKey"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12" v-if="userStore.roles.includes('admin')">
            <el-form-item label="租户编码" prop="tenantSn">
              <el-input
                v-model="form.tenantSn"
                placeholder="请输入租户编码"
                :maxlength="50"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="projectSn">
              <el-input
                v-model="form.projectSn"
                placeholder="请输入项目编码"
                :maxlength="50"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕编码" prop="screenSn">
              <el-input
                v-model="form.screenSn"
                placeholder="请输入屏幕编码"
                :maxlength="50"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="栏目编码" prop="columnSn">
              <el-input
                v-model="form.columnSn"
                placeholder="请输入栏目编码"
                :maxlength="50"
                show-word-limit
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 内容趋势 -->
    <el-dialog
      title="内容趋势"
      v-model="trendOpen"
      width="1200px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-button
        type="primary"
        plain
        icon="Plus"
        @click="trendAdd"
        style="margin-bottom: 10px"
        >新增
      </el-button>
      <el-table :data="trendList">
        <el-table-column
          label="热门时间"
          align="center"
          prop="publishTime"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.publishTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="数量" align="center" prop="publishCount">
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.publishCount)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.publishCount }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- <el-table-column label="显示顺序" align="center" prop="orderNum">
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.orderNum)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.orderNum }}</div>
            </el-tooltip>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="trendUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="trendDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="trendTotal"
        v-model:page="trendQueryParams.pageNum"
        v-model:limit="trendQueryParams.pageSize"
        @pagination="getTrendList"
        class="dialog-pagination"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="trendOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/修改内容趋势 -->
    <el-dialog
      :title="trendOpenTitle"
      v-model="trendAddOpen"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="trendAddRef"
        :model="trendAddForm"
        :rules="trendAddRules"
        label-width="80px"
      >
        <el-form-item label="热门时间" prop="publishTime">
          <el-date-picker
            clearable
            v-model="trendAddForm.publishTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择热门时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="数量" prop="publishCount">
          <el-input-number v-model="trendAddForm.publishCount" :min="0" />
        </el-form-item>
        <!-- <el-form-item label="显示顺序" prop="orderNum">
          <el-input-number v-model="trendAddForm.orderNum" :min="0" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="trendAddSubmit">确 定</el-button>
          <el-button @click="trendAddCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 相关内容 -->
    <el-dialog
      title="相关内容"
      v-model="relatedContentOpen"
      width="1200px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-button
        type="primary"
        plain
        icon="Plus"
        @click="relatedContentAdd"
        style="margin-bottom: 10px"
        >新增
      </el-button>
      <el-table :data="relatedContentList">
        <el-table-column
          label="文章唯一标识"
          align="center"
          prop="sn"
          width="110"
        >
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.sn)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.sn }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="文章标题"
          align="center"
          prop="title"
          width="300"
        >
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.title)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.title }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="数据源编码"
          align="center"
          prop="sourceSn"
          width="100"
        >
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.sourceSn)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.sourceSn }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="数据源名称"
          align="center"
          prop="sourceName"
          width="150"
        >
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.sourceName)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.sourceName }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="官网链接"
          align="center"
          prop="originalUrl"
          width="200"
        >
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.originalUrl)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.originalUrl }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="发布时间"
          align="center"
          prop="publishTime"
          width="150"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.publishTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="关键词"
          align="center"
          prop="keywords"
          width="150"
        >
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.keywords)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.keywords }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="描述" align="center" prop="summary" width="200">
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.summary)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.summary }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="作者" align="center" prop="author" width="100">
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.author)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.author }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="显示顺序"
          align="center"
          prop="orderNum"
          width="80"
        >
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.orderNum)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.orderNum }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
              scope.row.status === "0" ? "正常" : "停用"
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" width="150">
          <template #default="scope">
            <el-tooltip
              :content="String(scope.row.remark)"
              placement="top"
              :show-after="200"
              popper-class="custom-tooltip"
            >
              <div class="cell-content">{{ scope.row.remark }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          width="150"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="relatedContentUpdate(scope.row)"
              >修改
            </el-button>
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="relatedContentDelete(scope.row)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="relatedContentTotal"
        v-model:page="relatedContentQueryParams.pageNum"
        v-model:limit="relatedContentQueryParams.pageSize"
        @pagination="getRelatedContentList"
        class="dialog-pagination"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="relatedContentOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/修改相关内容 -->
    <el-dialog
      :title="relatedContentOpenTitle"
      v-model="relatedContentAddOpen"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="relatedContentAddRef"
        :model="relatedContentAddForm"
        :rules="relatedContentAddRules"
        label-width="100px"
      >
        <el-form-item label="文章标题" prop="title">
          <el-input
            v-model="relatedContentAddForm.title"
            placeholder="请输入文章标题"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="数据源编码" prop="sourceSn">
          <el-input
            v-model="relatedContentAddForm.sourceSn"
            placeholder="请输入数据源编码"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="数据源名称" prop="sourceName">
          <el-input
            v-model="relatedContentAddForm.sourceName"
            placeholder="请输入数据源名称"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="官网链接" prop="originalUrl">
          <el-input
            v-model="relatedContentAddForm.originalUrl"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="300"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker
            clearable
            v-model="relatedContentAddForm.publishTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择发布时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input
            v-model="relatedContentAddForm.keywords"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="描述" prop="summary">
          <el-input
            v-model="relatedContentAddForm.summary"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="1000"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="文章内容" prop="content">
          <editor
            v-model="relatedContentAddForm.content"
            :min-height="192"
            :key="editorKey"
          />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input
            v-model="relatedContentAddForm.author"
            placeholder="请输入内容"
            :maxlength="50"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="显示顺序" prop="orderNum">
          <el-input-number v-model="relatedContentAddForm.orderNum" :min="0" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="relatedContentAddForm.remark"
            type="textarea"
            placeholder="请输入内容"
            :maxlength="100"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="relatedContentAddSubmit"
            >确 定</el-button
          >
          <el-button @click="relatedContentAddCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Experts">
import { getToken } from "@/utils/auth";
import {
  addExperts,
  delExperts,
  getExperts,
  listExperts,
  updateExperts,
  listExpertsTrend,
  getExpertsTrend,
  addExpertsTrend,
  updateExpertsTrend,
  delExpertsTrend,
  listExpertsArticle,
  getExpertsArticle,
  addExpertsArticle,
  updateExpertsArticle,
  delExpertsArticle,
} from "@/api/screen/experts";
import { cleanHtmlContent } from "@/utils/htmlUtils";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();

const { proxy } = getCurrentInstance();
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const expertsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const trendOpen = ref(false);
const trendList = ref([]);
const trendTotal = ref(0);
const trendAddOpen = ref(false);
const relatedContentOpen = ref(false);
const relatedContentList = ref([]);
const relatedContentTotal = ref(0);
const relatedContentAddOpen = ref(false);
const trendOpenTitle = ref("");
const relatedContentOpenTitle = ref("");
const editorKey = ref(0);
const profileEditorKey = ref(0);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    expertsSn: null,
    expertsName: null,
    expertsNameEn: null,
    expertsPosition: null,
    expertsUnit: null,
    expertsTitle: null,
    avatar: null,
    keywords: null,
    researchArea: null,
    honorsAndAchievements: null,
    profile: null,
    status: null,
    deleteBy: null,
    deleteTime: null,
    tenantSn: null,
    projectSn: null,
    screenSn: null,
    columnSn: null,
  },
  rules: {
    expertsName: [
      { required: true, message: "姓名不能为空", trigger: "blur" },
      { max: 50, message: "姓名长度不能超过50个字符", trigger: "blur" },
    ],
    expertsNameEn: [
      { max: 50, message: "英文名长度不能超过50个字符", trigger: "blur" },
    ],
    expertsPosition: [
      // { required: true, message: "职务/头衔不能为空", trigger: "blur" },
      { max: 50, message: "职务/头衔长度不能超过50个字符", trigger: "blur" },
    ],
    expertsUnit: [
      // { required: true, message: "单位不能为空", trigger: "blur" },
      { max: 50, message: "单位长度不能超过50个字符", trigger: "blur" },
    ],
    expertsTitle: [
      { max: 50, message: "职称长度不能超过50个字符", trigger: "blur" },
    ],
    avatar: [{ required: true, message: "头像不能为空", trigger: "blur" }],
    keywords: [
      { max: 100, message: "关键词长度不能超过100个字符", trigger: "blur" },
    ],
    researchArea: [
      { max: 100, message: "研究领域长度不能超过100个字符", trigger: "blur" },
    ],
    honorsAndAchievements: [
      { max: 100, message: "荣誉/成就长度不能超过100个字符", trigger: "blur" },
    ],
    tenantSn: [
      { max: 50, message: "租户编码长度不能超过50个字符", trigger: "blur" },
    ],
    projectSn: [
      { required: true, message: "项目编码不能为空", trigger: "blur" },
      { max: 50, message: "项目编码长度不能超过50个字符", trigger: "blur" },
    ],
    screenSn: [
      { required: true, message: "屏幕编码不能为空", trigger: "blur" },
      { max: 50, message: "屏幕编码长度不能超过50个字符", trigger: "blur" },
    ],
    columnSn: [
      { required: true, message: "栏目编码不能为空", trigger: "blur" },
      { max: 50, message: "栏目编码长度不能超过50个字符", trigger: "blur" },
    ],
    remark: [
      { max: 100, message: "备注长度不能超过100个字符", trigger: "blur" },
    ],
  },
  trendQueryParams: {
    pageNum: 1,
    pageSize: 10,
    expertsSn: null,
  },
  trendAddForm: {},
  trendAddRules: {
    publishTime: [{ required: true, message: "日期不能为空", trigger: "blur" }],
    publishCount: [
      { required: true, message: "数量不能为空", trigger: "blur" },
    ],
    orderNum: [
      { required: true, message: "显示顺序不能为空", trigger: "blur" },
    ],
  },
  relatedContentQueryParams: {
    pageNum: 1,
    pageSize: 10,
    expertsSn: null,
  },
  relatedContentAddForm: {},
  relatedContentAddRules: {
    title: [
      { required: true, message: "标题不能为空", trigger: "blur" },
      { max: 100, message: "标题长度不能超过100个字符", trigger: "blur" },
    ],
    sourceSn: [
      { max: 50, message: "数据源编码长度不能超过50个字符", trigger: "blur" },
    ],
    sourceName: [
      { max: 50, message: "数据源名称长度不能超过50个字符", trigger: "blur" },
    ],
    originalUrl: [
      { required: true, message: "官网链接不能为空", trigger: "blur" },
      { max: 300, message: "官网链接长度不能超过300个字符", trigger: "blur" },
    ],
    keywords: [
      { max: 100, message: "关键词长度不能超过100个字符", trigger: "blur" },
    ],
    summary: [
      { max: 1000, message: "描述长度不能超过1000个字符", trigger: "blur" },
    ],
    author: [{ max: 50, message: "作者长度不能超过50个字符", trigger: "blur" }],
    orderNum: [
      { required: false, message: "显示顺序不能为空", trigger: "blur" },
    ],
    remark: [
      { max: 100, message: "备注长度不能超过100个字符", trigger: "blur" },
    ],
  },
});

const {
  queryParams,
  form,
  rules,
  trendQueryParams,
  trendAddForm,
  trendAddRules,
  relatedContentQueryParams,
  relatedContentAddForm,
  relatedContentAddRules,
} = toRefs(data);

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  businessType: null,
  headers: { Authorization: "Bearer " + getToken() },
  url: "",
});

/** 查询关键人列表 */
function getList() {
  loading.value = true;
  listExperts(queryParams.value).then((response) => {
    expertsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    expertsName: null,
    expertsNameEn: null,
    expertsPosition: null,
    expertsUnit: null,
    expertsTitle: null,
    avatar: null,
    keywords: null,
    researchArea: null,
    honorsAndAchievements: null,
    profile: "",
    projectSn: null,
    screenSn: null,
    columnSn: null,
    remark: null,
    publishTime: null,
  };
  if (userStore.roles.includes("admin")) {
    form.value.tenantSn = null;
  }
  proxy.resetForm("expertsRef");
  profileEditorKey.value += 1;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.expertsSn);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加关键人";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getExperts(_id).then((response) => {
    const responseData = response.data;
    const resetFields = [
      "expertsSn",
      "expertsName",
      "expertsNameEn",
      "expertsPosition",
      "expertsUnit",
      "expertsTitle",
      "avatar",
      "keywords",
      "researchArea",
      "honorsAndAchievements",
      "profile",
      "projectSn",
      "screenSn",
      "columnSn",
      "remark",
      "publishTime",
    ];
    form.value.id = responseData.id;
    resetFields.forEach((field) => {
      form.value[field] = responseData[field] || null;
    });
    if (userStore.roles.includes("admin")) {
      form.value.tenantSn = responseData.tenantSn;
    }
    open.value = true;
    title.value = "修改关键人";
  });
}

function handleCoverUpdate(value) {
  form.value.avatar = value;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["expertsRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateExperts(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addExperts(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.expertsSn || ids.value;
  proxy.$modal
    .confirm('是否确认删除关键人编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delExperts(Array.isArray(_ids) ? _ids : [_ids]);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "screen/experts/export",
    {
      ...queryParams.value,
    },
    `experts_${new Date().getTime()}.xlsx`
  );
}

function handleTrend(row) {
  trendQueryParams.value.pageNum = 1;
  trendQueryParams.value.expertsSn = row.expertsSn;
  trendAddForm.value.expertsSn = row.expertsSn;
  listExpertsTrend(trendQueryParams.value).then((response) => {
    trendList.value = response.rows;
    trendTotal.value = response.total;
    trendOpen.value = true;
  });
}

function getTrendList() {
  listExpertsTrend(trendQueryParams.value).then((response) => {
    trendList.value = response.rows;
    trendTotal.value = response.total;
  });
}

function trendFormReset() {
  const expertsSn = trendAddForm.value?.expertsSn;
  trendAddForm.value = {
    expertsSn: expertsSn,
    publishTime: null,
    publishCount: null,
    orderNum: null,
  };
  proxy.resetForm("trendAddRef");
}

function trendAdd() {
  trendFormReset();
  trendAddOpen.value = true;
  trendOpenTitle.value = "新增内容趋势";
}

function trendUpdate(row) {
  trendFormReset();
  const _id = row.id;
  getExpertsTrend(_id).then((response) => {
    const responseData = response.data;
    const resetFields = ["publishTime", "publishCount", "orderNum"];
    trendAddForm.value.id = responseData.id;
    resetFields.forEach((field) => {
      trendAddForm.value[field] = responseData[field] || null;
    });
    trendAddOpen.value = true;
    trendOpenTitle.value = "修改内容趋势";
  });
}

function trendDelete(row) {
  const _ids = row.id;
  proxy.$modal
    .confirm('是否确认删除内容趋势编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delExpertsTrend([_ids]);
    })
    .then(() => {
      getTrendList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function trendAddSubmit() {
  proxy.$refs["trendAddRef"].validate((valid) => {
    if (valid) {
      if (trendAddForm.value.id != null) {
        updateExpertsTrend(trendAddForm.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          trendAddOpen.value = false;
          getTrendList();
        });
      } else {
        addExpertsTrend(trendAddForm.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          trendAddOpen.value = false;
          getTrendList();
        });
      }
    }
  });
}

function trendAddCancel() {
  trendAddOpen.value = false;
  trendFormReset();
}

function handleRelatedContent(row) {
  relatedContentQueryParams.value.pageNum = 1;
  relatedContentQueryParams.value.expertsSn = row.expertsSn;
  relatedContentAddForm.value.expertsSn = row.expertsSn;
  listExpertsArticle(relatedContentQueryParams.value).then((response) => {
    relatedContentList.value = response.rows;
    relatedContentTotal.value = response.total;
    relatedContentOpen.value = true;
  });
}

function getRelatedContentList() {
  listExpertsArticle(relatedContentQueryParams.value).then((response) => {
    relatedContentList.value = response.rows;
    relatedContentTotal.value = response.total;
  });
}

function relatedContentFormReset() {
  const expertsSn = relatedContentAddForm.value?.expertsSn;
  relatedContentAddForm.value = {
    expertsSn: expertsSn,
    content: "",
    title: null,
    sourceSn: null,
    sourceName: null,
    originalUrl: null,
    publishTime: null,
    keywords: null,
    summary: null,
    author: null,
    orderNum: null,
    remark: null,
  };
  proxy.resetForm("relatedContentAddRef");
  editorKey.value += 1;
}

function relatedContentAdd() {
  relatedContentFormReset();
  relatedContentAddOpen.value = true;
  relatedContentOpenTitle.value = "新增相关内容";
}

function relatedContentUpdate(row) {
  relatedContentFormReset();
  const _id = row.id;
  getExpertsArticle(_id).then((response) => {
    const responseData = response.data;
    const resetFields = [
      "title",
      "sourceSn",
      "sourceName",
      "originalUrl",
      "publishTime",
      "keywords",
      "summary",
      "author",
      "orderNum",
      "remark",
    ];
    relatedContentAddForm.value.id = responseData.id;
    resetFields.forEach((field) => {
      relatedContentAddForm.value[field] = responseData[field] || null;
    });

    // 处理content字段，清理不规范的HTML标签
    relatedContentAddForm.value.content = cleanHtmlContent(
      responseData.content
    );

    relatedContentAddOpen.value = true;
    relatedContentOpenTitle.value = "修改相关内容";
  });
}

function relatedContentAddSubmit() {
  proxy.$refs["relatedContentAddRef"].validate((valid) => {
    if (valid) {
      if (relatedContentAddForm.value.id != null) {
        updateExpertsArticle(relatedContentAddForm.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          relatedContentAddOpen.value = false;
          getRelatedContentList();
        });
      } else {
        addExpertsArticle(relatedContentAddForm.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          relatedContentAddOpen.value = false;
          getRelatedContentList();
        });
      }
    }
  });
}

function relatedContentAddCancel() {
  relatedContentAddOpen.value = false;
  relatedContentFormReset();
}

function relatedContentDelete(row) {
  const _ids = row.id;
  proxy.$modal
    .confirm('是否确认删除相关内容编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delExpertsArticle([_ids]);
    })
    .then(() => {
      getRelatedContentList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 关键人导入 */
function handleImport() {
  upload.title = "关键人导入";
  upload.url =
    import.meta.env.VITE_APP_BASE_API + "/xty-screen/experts/excelImport";
  upload.businessType = 100;
  upload.open = true;
}

/** 关键人趋势导入 */
function handleImportTrend() {
  upload.title = "关键人趋势导入";
  upload.url =
    import.meta.env.VITE_APP_BASE_API + "/xty-screen/expertsTrend/excelImport";
  upload.businessType = 101;
  upload.open = true;
}

/** 关键人文章导入 */
function handleImportArticle() {
  upload.title = "关键人文章导入";
  upload.url =
    import.meta.env.VITE_APP_BASE_API +
    "/xty-screen/expertsArticle/excelImport";
  upload.businessType = 102;
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download(
    "xty-screen/experts/downloadTemplate?businessType=" + upload.businessType,
    {},
    `${upload.title}模版.xlsx`
  );
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

getList();
</script>

<style scoped lang="scss">
:deep(.queryForm) {
  .el-col-5 {
    max-width: 20%;
    flex: 0 0 20%;
  }
  .el-form-item {
    width: 100%;
    margin-right: 0;
  }
  .el-form-item__content {
    width: calc(100% - 90px);
  }
}

.cell-content {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 1.5;
}

.btns {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.btns .el-button {
  margin-left: 0;
}

.dialog-pagination :deep(.el-pagination) {
  right: 20px !important;
}
</style>
