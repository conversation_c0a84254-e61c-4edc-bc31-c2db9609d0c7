<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :style="{ width: props.width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <ul class="article-list" v-if="articles.length > 0">
          <li
            v-for="article in articles"
            :key="article.id"
            class="article-item"
          >
            <div class="article-title" @click="openDetail(article)" v-html="changeColor(article.title)">
            </div>
            <div class="article-sourceName">{{ article.sourceName }}</div>
            <div class="article-publishTime">{{ article.publishTime }}</div>
          </li>
        </ul>
        <div v-else>
          <div class="no-data">暂无数据</div>
        </div>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed, watch } from "vue";

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "自定义弹窗",
  },
  articles: {
    type: Array,
    default: () => [],
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1200,
  },
  item: {
    type: Object,
    default: () => ({}),
  },
  total: {
    type: Number,
    default: 0,
  },
});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      queryParams.value.pageNum = 1;
    }
  }
);

// 定义组件触发的事件
const emits = defineEmits([
  "update:visible",
  "openArticleDetail",
  "pagination",
]);
const openDetail = (item) => {
  emits("openArticleDetail", item);
};

const getList = () => {
  emits("pagination", queryParams.value);
};

// 关闭弹窗的方法
const closeDialog = () => {
  emits("update:visible", false);
};

// 处理遮罩层点击事件
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};

const changeColor = (str) => {
  let Str = str;
  if (Str) {
    let keywordsArr = [props.title];
    keywordsArr.map((keyitem, keyindex) => {
      if (keyitem && keyitem.length > 0) {
        // 匹配关键字正则
        let replaceReg = new RegExp(keyitem, "g");
        // 高亮替换v-html值
        let replaceString =
          '<span class="highlight"' +
          ' style="color: #ff7500;">' +
          keyitem +
          "</span>";
        Str = Str.replace(replaceReg, replaceString);
      }
    });
  }
  return Str;
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .article-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .article-item {
          display: flex;
          height: 40px;
          line-height: 40px;
          margin-bottom: 10px;
          font-size: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .article-title {
            cursor: pointer;
            width: calc(100% - 160px - 100px);
            font-size: 18px;
            // font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .article-sourceName {
            width: 160px;
            color: #fff;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .article-publishTime {
            width: 100px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 0 16px 8px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 16px;
          color: #ffffff;
          height: 40px;
          line-height: 40px;
        }

        .bg-box-content {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}

:deep(.pagination-container) {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump) {
  color: #f2f2f2;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination button:disabled) {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

:deep(.el-pager li) {
  background: #ffffff00 !important;
  color: #fff !important;

  &.is-active {
    color: #1890ff !important;
  }
}

.no-data {
  text-align: center;
  color: #fff;
  font-size: 16px;
  height: 100px;
  line-height: 80px;
}
</style>
