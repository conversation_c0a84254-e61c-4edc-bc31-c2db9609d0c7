import request from "@/utils/request";

// 查询大屏-关键企业列表
export function listEnterprise(query) {
  return request({
    url: "/screen/enterprise/list",
    method: "get",
    params: query,
  });
}

// 查询大屏-关键企业详细
export function getEnterprise(id) {
  return request({
    url: "/screen/enterprise/" + id,
    method: "get",
  });
}

// 新增大屏-关键企业
export function addEnterprise(data) {
  return request({
    url: "/screen/enterprise",
    method: "post",
    data: data,
  });
}

// 修改大屏-关键企业
export function updateEnterprise(data) {
  return request({
    url: "/screen/enterprise/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏-关键企业
export function delEnterprise(data) {
  return request({
    url: "/screen/enterprise/remove",
    method: "post",
    data: data,
  });
}

// 查询大屏-关键企业趋势列表
export function listEnterpriseTrend(query) {
  return request({
    url: "/screen/enterpriseTrend/list",
    method: "get",
    params: query,
  });
}

// 查询大屏-关键企业趋势详细
export function getEnterpriseTrend(id) {
  return request({
    url: "/screen/enterpriseTrend/" + id,
    method: "get",
  });
}

// 新增大屏-关键企业趋势
export function addEnterpriseTrend(data) {
  return request({
    url: "/screen/enterpriseTrend",
    method: "post",
    data: data,
  });
}

// 修改大屏-关键企业趋势
export function updateEnterpriseTrend(data) {
  return request({
    url: "/screen/enterpriseTrend/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏-关键企业趋势
export function delEnterpriseTrend(data) {
  return request({
    url: "/screen/enterpriseTrend/remove",
    method: "post",
    data: data,
  });
}

// 查询关键企业文章列表
export function listEnterpriseArticle(query) {
  return request({
    url: "/screen/enterpriseArticle/list",
    method: "get",
    params: query,
  });
}

// 查询关键企业文章详细
export function getEnterpriseArticle(id) {
  return request({
    url: "/screen/enterpriseArticle/" + id,
    method: "get",
  });
}

// 新增关键企业文章
export function addEnterpriseArticle(data) {
  return request({
    url: "/screen/enterpriseArticle",
    method: "post",
    data: data,
  });
}

// 修改关键企业文章
export function updateEnterpriseArticle(data) {
  return request({
    url: "/screen/enterpriseArticle/edit",
    method: "post",
    data: data,
  });
}

// 删除关键企业文章
export function delEnterpriseArticle(data) {
  return request({
    url: "/screen/enterpriseArticle/remove",
    method: "post",
    data: data,
  });
}
