import request from '@/utils/request'

// 查询前沿技术报告列表
export function listTechnicalReport(query) {
  return request({
    url: '/screen/technicalReport/list',
    method: 'get',
    params: query
  })
}

// 查询前沿技术报告详细
export function getTechnicalReport(id) {
  return request({
    url: '/screen/technicalReport/' + id,
    method: 'get'
  })
}

// 新增前沿技术报告
export function addTechnicalReport(data) {
  return request({
    url: '/screen/technicalReport',
    method: 'post',
    data: data
  })
}

// 修改前沿技术报告
export function updateTechnicalReport(data) {
  return request({
    url: '/screen/technicalReport/edit',
    method: 'post',
    data: data
  })
}

// 删除前沿技术报告
export function delTechnicalReport(data) {
  return request({
    url: '/screen/technicalReport/remove',
    method: 'post',
    data: data
  })
}
