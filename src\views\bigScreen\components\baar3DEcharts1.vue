<template>
  <div class="chartBox">
    <div class="chart" ref="chartContainer"></div>
  </div>
</template>
  
<script setup>
import * as echarts from 'echarts';
import 'echarts-gl';
import { technical3dData } from "@/api/bigScreen/index";

const props = defineProps({
  sccenId: {
    type: Number,
    default: 1,
  },
  type: {
    type: Number,
    default: 1,
  },
  show: {
    type: Boolean,
    default: false,
  }
});

const chartContainer = ref(null);
var myChart = null;
const option = ref(null);
const labelList = ref([]);

const getPie3D = (pieData, internalDiameterRatio) => {
  let sumValue = 0;
  let startValue = 0;
  const k = 1 - internalDiameterRatio;

  // 计算总价值
  sumValue = pieData.reduce((acc, item) => acc + item.value, 0);
  // 设定最大高度，可按需调整
  const maxHeight = 20;

  const series = pieData.map((item, i) => {
    const startRatio = startValue / sumValue;
    const endRatio = (startValue + item.value) / sumValue;
    startValue += item.value;
    // 计算当前扇形的百分比高度
    const percentageHeight = (item.value / sumValue) * maxHeight;

    return {
      name: item.name || `series${i}`,
      type: 'surface',
      parametric: true,
      wireframe: { show: false },
      pieData: item,
      pieStatus: { selected: false, hovered: false, k },
      center: ['80%', '100%'],
      radius: '60%',
      itemStyle: item.itemStyle || {},
      parametricEquation: getParametricEquation(startRatio, endRatio, false, false, k, percentageHeight)
    };
  });

  const boxHeight = maxHeight;
  return {
    // legend: {
    //   padding: [25, 10],
    //   data: pieData.map(item => item.name),
    //   x: 'right',
    //   y: 'top',
    //   orient: 'horizontal',
    //   itemGap: 15,
    //   textStyle: { color: '#A1E2FF', fontSize: '12px' },
    //   itemHeight: 10,
    //   itemWidth: 10,
    //   show: true,
    //   icon: 'circle',
    //   // 格式化图例，显示名称和 value 值
    //   formatter: (name) => {
    //     const item = pieData.find(item => item.name === name);
    //     return item ? `${name} (${item.value})` : name;
    //   }
    // },
    tooltip: {
      // 格式化提示框，显示名称和 value 值
      formatter: (params) => {
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          const item = pieData.find(item => item.name === params.seriesName);
          return item ? `${params.seriesName}<br/>值: ${item.value}` : '';
        }
      }
    },
    xAxis3D: { min: -1, max: 1 },
    yAxis3D: { min: -1, max: 1 },
    zAxis3D: { min: -1, max: 1 },
    grid3D: {
      show: false,
      boxHeight,
      top: '5%',
      left: '0%',
      viewControl: {
        alpha: 25,
        distance: 135,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        panSensitivity: 0,
        autoRotate: false
      }
    },
    series
  };
};

const getParametricEquation = (startRatio, endRatio, isSelected, isHovered, k, percentageHeight) => {
  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;

  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }

  k = typeof k !== 'undefined' ? k : 1 / 3;
  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  const hoverRate = isHovered ? 1.05 : 1;

  return {
    u: { min: -Math.PI, max: Math.PI * 3, step: Math.PI / 32 },
    v: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
    x: (u, v) => {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: (u, v) => {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: (u, v) => {
      if (Math.sin(v) > 0) {
        return percentageHeight;
      }
      return -0.1;
    }
  };
};


onMounted(() => {
  if (!chartContainer.value) return;
  myChart = echarts.init(chartContainer.value);
  technical3dData({ projectSn: '1', screenSn: props.sccenId, columnSn: '1', isForeign: props.type }).then(res => {
    labelList.value = res.data.map(row => {
      return {
        name: row.name,
        value: Number(row.publishCount),
        color: row.backgroundColor
      }
    })
    labelList.value.sort((a, b) => b.value - a.value);
    option.value = getPie3D(labelList.value, 0.85);
    myChart.setOption(option.value);
  })
});

watch(() => props.show, (newType) => {
  if (newType) {
    setTimeout(() => {
      myChart?.resize();
    }, 1);
  }
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
  }
});
</script>
  
<style scoped lang="scss">
.chartBox {
  width: 100%;
  height: 100%;
  position: relative;

  .chart {
    width: 100%;
    height: 100%;
    min-height: 200px;
  }
}
</style>