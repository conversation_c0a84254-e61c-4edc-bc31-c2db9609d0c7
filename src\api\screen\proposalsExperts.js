import request from '@/utils/request'

// 查询政策提案人员列表
export function listProposalsExperts(query) {
  return request({
    url: '/screen/proposalsExperts/list',
    method: 'get',
    params: query
  })
}

// 查询政策提案人员详细
export function getProposalsExperts(id) {
  return request({
    url: '/screen/proposalsExperts/' + id,
    method: 'get'
  })
}

// 新增政策提案人员
export function addProposalsExperts(data) {
  return request({
    url: '/screen/proposalsExperts',
    method: 'post',
    data: data
  })
}

// 修改政策提案人员
export function updateProposalsExperts(data) {
  return request({
    url: '/screen/proposalsExperts/edit',
    method: 'post',
    data: data
  })
}

// 删除政策提案人员
export function delProposalsExperts(data) {
  return request({
    url: '/screen/proposalsExperts/remove',
    method: 'post',
    data: data
  })
}
