import request from '@/utils/request'

// 查询关键人文章列表
export function listExpertsArticle(query) {
  return request({
    url: '/screen/expertsArticle/list',
    method: 'get',
    params: query
  })
}

// 查询关键人文章详细
export function getExpertsArticle(id) {
  return request({
    url: '/screen/expertsArticle/' + id,
    method: 'get'
  })
}

// 新增关键人文章
export function addExpertsArticle(data) {
  return request({
    url: '/screen/expertsArticle',
    method: 'post',
    data: data
  })
}

// 修改关键人文章
export function updateExpertsArticle(data) {
  return request({
    url: '/screen/expertsArticle',
    method: 'put',
    data: data
  })
}

// 删除关键人文章
export function delExpertsArticle(id) {
  return request({
    url: '/screen/expertsArticle/' + id,
    method: 'delete'
  })
}
