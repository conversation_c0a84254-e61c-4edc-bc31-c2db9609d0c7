<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :style="{ width: props.width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <!-- <div class="bg-box-title">美国提案分布情况</div> -->
          <div class="bg-box-content content-flex">
            <sankTimeLine
              :timelineEvents="timelineData"
              style="width: 1100px; height: 284px"
            ></sankTimeLine>
          </div>
        </div>
        <div class="bg-box" v-for="(item, index) in detailData" :key="index">
          <div class="bg-box-title">{{ item.analysisTitle }}</div>
          <div class="bg-box-content">
            {{ item.analysisContent }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed, watch, ref } from "vue";
import sankTimeLine from "../components/sankTimeLine";

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "自定义弹窗",
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1280,
  },
  detailData: {
    type: Array,
    default: () => [],
  },
  timelineData: {
    type: Array,
    default: () => [],
  },
});

// 定义组件触发的事件
const emits = defineEmits(["update:visible"]);

// 关闭弹窗的方法
const closeDialog = () => {
  emits("update:visible", false);
};

// 处理遮罩层点击事件
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.9);
          .bg-box-content-list {
            padding: 0 0 10px 15px;
            font-size: 14px;
            color: #ffffffc6;

            span {
              font-weight: 600;
              color: #ffffff;
              margin-right: 10px;
            }
          }

          :deep(.el-table__header th) {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          :deep(.el-table__body td) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }

          :deep(.el-descriptions__label) {
            background-color: #1f3850;
            color: rgba(255, 255, 255);
            font-size: 16px;
            text-align: center;
          }

          :deep(.el-descriptions__content) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            text-align: center;
          }
        }

        .content-flex {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}
</style>
