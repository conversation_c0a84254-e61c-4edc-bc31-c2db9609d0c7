<template>
  <div
    v-if="visible"
    class="custom-dialog-mask"
    @click="handleMaskClick"
    :style="{ zIndex: currentZIndex }"
  >
    <div
      class="custom-dialog"
      :style="{ width: props.width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ tianExpertsDetail?.proposalsExperts }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">人物简介</div>
          <div class="bg-box-content">
            <div class="jianjie">
              <div class="touxiang">
                <img
                  v-if="tianExpertsDetail.proposalsExpertsCover"
                  :src="baseUrl + tianExpertsDetail.proposalsExpertsCover"
                  alt=""
                />
                <el-empty v-else :image-size="106" style="padding: 0" />
              </div>
              <div class="text">
                <div style="margin-bottom: 10px">
                  <span style="margin-right: 50px">{{
                    tianExpertsDetail.belongToGroup
                  }}</span>
                  <span>{{ tianExpertsDetail.belongToArea }}</span>
                </div>
                <div>{{ tianExpertsDetail.expertSummary }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">相关提案</div>
          <div class="bg-box-content">
            <el-table
              :data="tianExpertsDetail.proposals"
              border
              style="width: 100%"
            >
              <el-table-column
                prop="proposalsTitle"
                align="center"
                label="提案名称"
              >
                <template #default="scope">
                  <div
                    @click="openpOlicyRiskContent(scope.row.proposalsSn)"
                    style="color: #0ec2f4e6; cursor: pointer; font-weight: bold"
                  >
                    {{ scope.row.proposalsTitle }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="summary" align="center" label="提案摘要" />
              <el-table-column
                prop="publishTime"
                align="center"
                label="提案时间"
              />
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch } from "vue";
import { getNewZIndex, getBaseZIndex } from "@/utils/zIndexManager";

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1000,
  },
  tianExpertsDetail: {
    type: Object,
    default: () => {},
  },
});

// 当前对话框的 z-index
const currentZIndex = ref(getBaseZIndex());

const emits = defineEmits(["update:visible", "openpOlicyRiskContent"]);

// 提升组件到最上层的方法
const bringToFront = () => {
  currentZIndex.value = getNewZIndex();
};

// 对外暴露方法
defineExpose({
  bringToFront,
});

watch(
  () => props.visible,
  (value) => {
    activeContent.value = "proposalsContent";
    // 当对话框变为可见时，更新 z-index
    if (value) {
      bringToFront();
    }
  }
);

const closeDialog = () => {
  emits("update:visible", false);
};

const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};

// 定义一个响应式变量来记录当前选中的内容类型
const activeContent = ref("proposalsContent");

const openpOlicyRiskContent = (id) => {
  emits("openpOlicyRiskContent", id);
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  // z-index: 1000; // 移除固定 z-index，使用动态值

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 40px;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        position: relative;
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .toggle-divs {
          position: absolute;
          display: flex;
          top: 5px;
          right: 10px;

          div {
            width: 48px;
            height: 30px;
            background-color: #3a4d68ff;
            color: white;
            padding: 5px 10px;
            cursor: pointer;
            user-select: none; // 禁止文本选中

            &:hover {
              background-color: #1d3046;
            }

            // 选中状态的高亮样式
            &.active {
              background-color: #ff9d00ff;
              color: white;
            }
          }
        }

        .bg-box-content {
          // display: flex;
          // justify-content: center;
          font-size: 16px;

          :deep(.el-table__header th) {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          :deep(.el-table__body td) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }

          :deep(.el-descriptions__label) {
            background-color: #1f3850;
            color: rgba(255, 255, 255);
            font-size: 16px;
            text-align: center;
          }

          :deep(.el-descriptions__content) {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            text-align: center;
          }

          .jianjie {
            display: flex;

            .touxiang {
              width: 106px;
              height: 143px;
              display: flex;
              align-items: center;
              justify-content: center;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .text {
              flex: 1;
              padding-left: 20px;
            }
          }
        }
      }
    }
  }
}
::v-deep(.el-empty__description) {
  display: none;
}
</style>
