import request from '@/utils/request'

// 查询政策提案列表
export function listProposals(query) {
  return request({
    url: '/screen/proposals/list',
    method: 'get',
    params: query
  })
}

// 查询政策提案详细
export function getProposals(id) {
  return request({
    url: '/screen/proposals/' + id,
    method: 'get'
  })
}

// 新增政策提案
export function addProposals(data) {
  return request({
    url: '/screen/proposals',
    method: 'post',
    data: data
  })
}

// 修改政策提案
export function updateProposals(data) {
  return request({
    url: '/screen/proposals/edit',
    method: 'post',
    data: data
  })
}

// 删除政策提案
export function delProposals(data) {
  return request({
    url: '/screen/proposals/remove',
    method: 'post',
    data: data
  })
}
