import request from "@/utils/request";

// 查询大屏-风险态势趋势数据列表
export function listTrendAnalysis(query) {
  return request({
    url: "/screen/trendAnalysis/list",
    method: "get",
    params: query,
  });
}

// 查询大屏-风险态势趋势数据详细
export function getTrendAnalysis(id) {
  return request({
    url: "/screen/trendAnalysis/" + id,
    method: "get",
  });
}

// 新增大屏-风险态势趋势数据
export function addTrendAnalysis(data) {
  return request({
    url: "/screen/trendAnalysis",
    method: "post",
    data: data,
  });
}

// 修改大屏-风险态势趋势数据
export function updateTrendAnalysis(data) {
  return request({
    url: "/screen/trendAnalysis/edit",
    method: "post",
    data: data,
  });
}

// 删除大屏-风险态势趋势数据
export function delTrendAnalysis(data) {
  return request({
    url: "/screen/trendAnalysis/remove",
    method: "post",
    data: data,
  });
}
