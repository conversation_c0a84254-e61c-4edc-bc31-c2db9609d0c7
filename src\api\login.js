import request from '@/utils/request'

// 登录方法
export function login(username, password, code, smsCode, uuid) {
  const data = {
    username,
    password,
    code,
    smsCode,
    uuid
  }
  return request({
    url: '/auth/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 刷新方法
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/user/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/code',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
// 获取短信验证码
export function getSmsCode(data) {
  return request({
    url: '/auth/getCode',
    headers: {
      isToken: false
    },
    method: 'post',
    timeout: 20000,
    data: data
  })
}
// 找回密码时获取验证码
export function getModifySmsCode(data) {
  return request({
    url: 'ruoyi-auth/modify/getCode',
    headers: {
      isToken: false
    },
    method: 'post',
    timeout: 20000,
    data: data
  })
}
// 找回密码确认修改接口
export function editModifyPwd(data) {
  return request({
    url: 'ruoyi-auth/modify/pwd',
    headers: {
      isToken: false
    },
    method: 'post',
    timeout: 20000,
    data: data
  })
}