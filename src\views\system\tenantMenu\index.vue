<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px"
      >
      <el-form-item label="菜单名称" prop="menuName">
        <el-input v-model="queryParams.menuName" placeholder="请输入菜单名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" style="margin: 0 0 15px;">
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="tenantMenuList" row-key="menuId"
      :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column prop="icon" label="图标" align="center" width="100">
        <template #default="scope">
          <svg-icon :icon-class="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column label="菜单别名" prop="tenantMenuName">
        <template #default="scope">
          <el-input v-model="scope.row.tenantMenuName" />
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="排序" width="60"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handEdit(scope.row)" v-hasPermi="['article:tenantMenu:edit']">保存</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="菜单" prop="menuId">
          <el-input v-model="form.menuId" placeholder="请输入菜单" />
        </el-form-item>
        <el-form-item label="默认显示menu_id对应的menu_name,租户可以重命名" prop="menuName">
          <el-input v-model="form.menuName" placeholder="请输入默认显示menu_id对应的menu_name,租户可以重命名" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { listTenantMenu, getTenantMenu, delTenantMenu, addTenantMenu, updateTenantMenu } from "@/api/system/tenantMenu";
import { listMenu } from "@/api/system/menu";
import { ElMessage, ElMessageBox } from 'element-plus';

// 假设 handleTree 是一个自定义方法，需要在这里导入或定义
const handleTree = (data, id) => {
  // 实现 handleTree 方法
  return data;
};

// 假设 resetForm 是一个自定义方法，需要在这里导入或定义
const resetForm = (formName) => {
  // 实现 resetForm 方法
  if (formName === 'form') {
    form.tenantId = null;
    form.menuId = null;
    form.menuName = null;
  } else if (formName === 'queryForm') {
    queryParams.menuId = null;
    queryParams.menuName = null;
  }
};

const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const tenantMenuList = ref([]);
const title = ref("");
const open = ref(false);
const queryParams = reactive({
  menuId: null,
  menuName: null
});
const form = reactive({});
const rules = reactive({
  menuId: [
    { required: true, message: "菜单不能为空", trigger: "blur" }
  ],
  menuName: [
    {
      required: true, message: "默认显示menu_id对应的menu_name,租户可以重命名不能为空", trigger: "blur"
    }
  ]
});
const isExpandAll = ref(false);
const refreshTable = ref(true);

const getList = () => {
  loading.value = true;
  listMenu(queryParams).then(response => {
    tenantMenuList.value = handleTree(response.data.sort((a, b) => a.orderNum - b.orderNum), "menuId");
    loading.value = false;
  });
};

const cancel = () => {
  open.value = false;
  reset();
};

const reset = () => {
  resetForm("form");
};

const handleQuery = () => {
  getList();
};

const resetQuery = () => {
  resetForm("queryForm");
  handleQuery();
};

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.tenantId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

const handEdit = (row) => {
  updateTenantMenu({ menuId: row.menuId, menuName: row.tenantMenuName }).then(response => {
    ElMessage.success("保存成功");
    getList();
  });
};

const toggleExpandAll = () => {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.rightBread {
  background-color: #FFFFFF;
  padding: 0px 5px 0px 15px;
  height: 40px;
  margin-bottom: 15px;

  :deep .el-breadcrumb {
    .el-breadcrumb__inner {
      color: #1d6285;
      line-height: 40px;
    }

    .el-breadcrumb__inner.is-link {
      color: #3B91FF;
      font-size: 16px;
    }
  }
}
</style>