<template>
  <div class="timeline">
    <div
      v-for="(event, index) in timelineEvents"
      :key="index"
      class="timeline-item"
    >
      <div class="timeline-node"></div>
      <div class="description-box odd-box" v-if="index % 2 === 1">
        <div class="date">{{ event.nodesTime }}</div>
        <div></div>
        <div class="timeline-content" :class="'timeline-bg' + index">
          <div class="description">{{ event.nodesSummary }}</div>
        </div>
      </div>
      <div class="description-box even-box" v-else>
        <div class="timeline-content" :class="'timeline-bg' + index">
          <div class="description">{{ event.nodesSummary }}</div>
        </div>
        <div></div>
        <div class="date">{{ event.nodesTime }}</div>
      </div>
      <div
        v-if="index < timelineEvents.length - 1"
        class="timeline-arrow"
      ></div>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();

const props = defineProps({
  timelineEvents: {
    type: Array,
    default: [],
  },
});
</script>

<style scoped lang="scss">
.timeline {
  position: relative;
  display: flex;
  justify-content: center;
  height: 100%;
  padding: 10px 5px;

  .timeline-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 33.3%;

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: calc(50% + 10px);
      width: calc(100% - 30px);
      height: 3px;
      background: #2a769d;
      transform: translateY(-50%);
      z-index: 0;
    }

    &:last-child::after {
      display: none;
    }

    .timeline-node {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 24px;
      height: 24px;
      background: transparent;
      border: 2px solid #2a769d;
      border-radius: 50%;
      z-index: 3;

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 10px;
        height: 10px;
        background: #2a769d;
        border-radius: 50%;
      }
    }

    .description-box {
      position: relative;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .date {
        position: absolute;
        top: calc(50% + 30px);
        transform: translateY(-50%);
        font-size: 16px;
        color: #ffffff;
        font-weight: bold;
      }

      .timeline-content {
        min-width: 100px;
        max-height: calc(50% - 30px);
        height: 80px;
        padding: 10px 10%;
        background-image: url("@/assets/bigScreen/timeLineBg1.png");
        background-size: 100% 100%;
        position: relative;
        transition: transform 0.3s ease;
        text-align: center;
        z-index: 3;

        &:hover {
          transform: translateX(5px);
        }

        .title {
          font-size: 18px;
          color: #ffffff;
          margin-bottom: 8px;
        }

        .description {
          font-weight: 400;
          font-size: 12px;
          line-height: 20px;
          color: #ffffff;
          text-align: left;
          max-height: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          white-space: normal;
          word-break: break-all;
        }
      }

      .timeline-bg1{
        background-image: url("@/assets/bigScreen/timeLineBg2.png");
      }
      .timeline-bg2{
        background-image: url("@/assets/bigScreen/timeLineBg3.png");
      }
      .timeline-bg3{
        background-image: url("@/assets/bigScreen/timeLineBg4.png");
      }
      .timeline-bg4{
        background-image: url("@/assets/bigScreen/timeLineBg5.png");
      }

      // 添加连接线样式
      &::before {
        content: "";
        position: absolute;
        // top: 0;
        bottom: calc(50% + 10px);
        left: 50%;
        width: 2px;
        height: calc(50% - 90px);
        background: #2a769d;
        z-index: 2;
      }
    }

    .odd-box {
      .date {
        top: calc(50% - 30px);
      }

      &::before {
        bottom: 0;
        top: calc(50% + 10px);
      }
    }

    .timeline-arrow {
      position: absolute;
      top: 50%;
      right: calc(-50% + 14px);
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-left: 12px solid #2a769d;
      z-index: 1;
    }
  }
}
</style>
