import request from "@/utils/request";

// 查询专家观点列表
export function listViewpoint(query) {
  return request({
    url: "/screen/viewpoint/list",
    method: "get",
    params: query,
  });
}

// 查询专家观点详细
export function getViewpoint(id) {
  return request({
    url: "/screen/viewpoint/" + id,
    method: "get",
  });
}

// 新增专家观点
export function addViewpoint(data) {
  return request({
    url: "/screen/viewpoint",
    method: "post",
    data: data,
  });
}

// 修改专家观点
export function updateViewpoint(data) {
  return request({
    url: "/screen/viewpoint/edit",
    method: "post",
    data: data,
  });
}

// 删除专家观点
export function delViewpoint(data) {
  return request({
    url: "/screen/viewpoint/remove",
    method: "post",
    data: data,
  });
}
