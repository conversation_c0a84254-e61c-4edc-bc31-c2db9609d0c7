<template>
  <v-scale-screen fullScreen width="1920" height="1080">
    <div class="bigMap">
      <div class="bigMap-bg">
        <div class="top">
          <div class="center-left">
            <div :class="{ 'tableTitle': true, 'tableTitleLight': activeKey != 0 }" @click="changeTabsFun(0)">
              人工智能
            </div>
          </div>
          <div class="center-content">
            <div class="text">全球开源科技情报地平线扫描分析平台</div>
          </div>
          <div class="center-right">
            <div :class="{ 'tableTitle': true, 'tableTitleLight': activeKey != 1 }" @click="changeTabsFun(1)">
              网络安全
            </div>
          </div>
        </div>

        <div class="bottom">
          <tabOne v-if="activeKey == 0" />
          <tabTwo v-else-if="activeKey == 1" />
        </div>
      </div>
    </div>
  </v-scale-screen>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import tabOne from "./tabOne";
import tabTwo from "./tabTwo";
// import sankTimeLine from "./secondLevel/sankTimeLine";
import sankTimeLine from "./components/sankTimeLine";

const activeKey = ref(0);
const currentTime = ref("");
const timer = ref(null);

onMounted(() => {
  // timerFn()
});

const timerFn = () => {
  timer.value = setInterval(() => {
    currentTime.value = new Date();
    let weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    let week = weekDays[currentTime.value.getDay()];
  }, 1000);
};

onUnmounted(() => {
  clearInterval(timer.value);
});

const changeTabsFun = (index) => {
  activeKey.value = index;
};
</script>

<style lang="scss" scoped>
.bigMap {
  background: #081F46;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0px;
  margin: 0px;
  font-family: PingFang SC, PingFang SC;

  .bigMap-bg {
    // background: url("../../assets/bigScreenThree/bg.png") 0px 0px no-repeat;
    // background-size: 100% 100% !important;
    // background-size: cover;
    font-size: 14px;
    color: #ffffff;
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0px;
    margin: 0px;
    display: flex;
    flex-direction: column;
    cursor: default;

    .top {
      height: 74px;
      display: flex;

      //左边
      .center-left {
        width: 178px;
        height: 74px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      //中间
      .center-content {
        width: 1623px;
        height: 74px;
        background: url("../../assets/bigScreen/Btitle.png") no-repeat 0px 0px !important;
        background-size: 100% 100% !important;

        .text {
          height: 55px;
          font-weight: 800;
          font-size: 27px;
          color: #FFFFFF;
          line-height: 54px;
          letter-spacing: 8px;
          text-align: center;
          line-height: 55px;
        }
      }

      //右边
      .center-right {
        width: 178px;
        height: 74px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .tableTitle {
        width: 136px;
        height: 28px;
        background: url("../../assets/bigScreen/Btable.png") no-repeat 0px 0px !important;
        background-size: 100% 100% !important;
        text-align: center;
        line-height: 28px;
        font-weight: 800;
        font-size: 19px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .tableTitleLight {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .bottom {
      flex: 1;
      height: calc(100% - 74px);
      position: relative;
      padding: 0 10px 0px 10px;
    }
  }
}
</style>
