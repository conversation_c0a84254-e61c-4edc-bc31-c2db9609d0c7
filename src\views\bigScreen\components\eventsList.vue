<template>
  <div class="remengwenzhang-box">
    <div
      class="scroll-wrapper"
      ref="scrollWrapper2"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @scroll="updateScrollbar"
    >
      <div class="scroll-content" ref="scrollContent">
        <div
          class="remengwenzhang-list"
          v-for="(item, index) in dataList"
          :key="index"
          @click="openNewView(item)"
        >
          <div class="sourceName">{{ item.eventsTitle }}</div>
          <div class="title">{{ item.summary }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();

const emits = defineEmits(["openArticleDetail"]);

const props = defineProps({
  dataList: {
    type: Array,
    default: [],
  },
});

// 定义响应式数据
const scrollTimer = ref(null);
const isHovered = ref(false);
const scrollStep = ref(1);
const scrollWrapper2 = ref(null);
const scrollContent = ref(null);

const init = async () => {
  try {
    proxy.$nextTick(() => {
      startScroll();
    });
  } catch (error) {}
};

const startScroll = () => {
  clearScrollTimer();
  if (!scrollWrapper2.value || !scrollContent.value) return;

  scrollTimer.value = setInterval(() => {
    if (isHovered.value) return;

    if (
      scrollWrapper2.value.scrollTop >=
      scrollContent.value.scrollHeight - scrollWrapper2.value.clientHeight
    ) {
      scrollWrapper2.value.scrollTop = 0;
    } else {
      scrollWrapper2.value.scrollTop += scrollStep.value;
    }
    updateScrollbar();
  }, 30);
};

const clearScrollTimer = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value);
    scrollTimer.value = null;
  }
};

const handleMouseEnter = () => {
  isHovered.value = true;
};

const handleMouseLeave = () => {
  isHovered.value = false;
  startScroll();
};

const openNewView = (item) => {
  emits("openEventsDetail", item);
};

const updateScrollbar = () => {
  if (!scrollWrapper2.value) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollWrapper2.value;
  const scrollPercent = clientHeight / scrollHeight;
  const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);
  const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;

  document.documentElement.style.setProperty(
    "--scrollbar-height",
    `${scrollbarHeight}px`
  );
  document.documentElement.style.setProperty(
    "--scrollbar-top",
    `${scrollbarTop}px`
  );
};

onMounted(() => {
  init();
  updateScrollbar();
});

onBeforeUnmount(() => {
  clearScrollTimer();
});
</script>

<style lang="scss" scoped>
.remengwenzhang-box {
  width: 100%;
  height: 100%;
  padding: 10px 15px 0;
  // background: rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;

  .scroll-wrapper {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    position: relative;

    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &::after {
    content: "";
    position: absolute;
    top: 20px;
    right: 0;
    height: calc(100% - 40px);
    width: 6px;
    background: rgba(16, 216, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
  }

  &:hover {
    &::after,
    .scroll-bar {
      opacity: 1;
    }
  }

  .remengwenzhang-list {
    position: relative;
    height: 40px;
    // padding: 10px 0px 10px 20px;
    // padding-left: 20px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    border: 1px solid #ffffff;
    border-bottom-width: 0;

    .time,
    .sourceName,
    .title {
      border-left: 1px solid #ffffff;
      padding: 0 5px;
      width: 150px;
      color: rgba(255, 255, 255, 0.7);
      text-align: left;
      font-family: "Source Han Sans CN";
      font-size: 18px;
      font-weight: 400;
      line-height: 38px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .time {
      border-left: none;
      text-align: center;
    }

    .title {
      width: 330px;
      font-weight: 700;
      color: #00abf4;
    }

    .block {
      position: absolute;
      left: 0px;
      top: 6px;
      width: 10px;
      height: 10px;
      border-radius: 1px;
      background: #1bdcff;
    }

    &:last-child {
      border-bottom-width: 1px;
    }
  }
}
</style>
