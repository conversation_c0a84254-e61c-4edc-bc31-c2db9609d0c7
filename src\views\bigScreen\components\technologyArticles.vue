<template>
  <div class="technologyArticlesBox">
    <div class="technologyArticles" v-for="(item, index) in hotTechnologyList">
      <div class="technologyArticlesPng" @click="openHotTechnologyDetails(item)">
        <img :src="baseUrl + item.cover" alt="">
      </div>
      <div class="technologyArticlesBg"></div>
    </div>
  </div>
</template>
    
<script setup>
import { technicalReportData } from "@/api/bigScreen/index";
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const emits = defineEmits(["openHotTechnology"]);
const props = defineProps({
  sccenId: {
    type: Number,
    default: 1,
  },
});

const hotTechnologyList = ref([])

const openHotTechnologyDetails = (data) => {
  emits("openHotTechnology", { ...data, title: data.reportName });
};

onMounted(() => {
  technicalReportData({ projectSn: '1', screenSn: props.sccenId, columnSn: '1' }).then(res => {
    hotTechnologyList.value = res.data
  })
});

</script>
    
<style lang="scss" scoped>
.technologyArticlesBox {
  display: flex;
  justify-content: space-around;
  height: 100%;
  padding: 20px 15px 12px;

  .technologyArticles {
    position: relative;
    width: 134px;
    height: 100%;

    .technologyArticlesPng {
      position: relative;
      height: 100%;
      z-index: 2;
      display: flex;
      justify-content: center;

      img {
        cursor: pointer;
        position: absolute;
        top: 10px;
        height: 120px;
        box-shadow: 0px 0px 20px 4px skyblue;
      }
    }

    .technologyArticlesBg {
      position: absolute;
      bottom: 0;
      width: 134px;
      height: 80px;
      background-image: url('@/assets/bigScreen/articleBase.png');
      background-size: 100%;
      z-index: 1;
    }
  }
}
</style>
    