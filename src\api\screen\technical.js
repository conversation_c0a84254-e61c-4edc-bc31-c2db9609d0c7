import request from '@/utils/request'

// 查询前沿技术热点列表
export function listTechnical(query) {
  return request({
    url: '/screen/technical/list',
    method: 'get',
    params: query
  })
}

// 查询前沿技术热点详细
export function getTechnical(id) {
  return request({
    url: '/screen/technical/' + id,
    method: 'get'
  })
}

// 新增前沿技术热点
export function addTechnical(data) {
  return request({
    url: '/screen/technical',
    method: 'post',
    data: data
  })
}

// 修改前沿技术热点
export function updateTechnical(data) {
  return request({
    url: '/screen/technical/edit',
    method: 'post',
    data: data
  })
}

// 删除前沿技术热点
export function delTechnical(data) {
  return request({
    url: '/screen/technical/remove',
    method: 'post',
    data: data
  })
}
