import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvqjICLTkFAahJQzIuJNmpaJWmh+poSlZGHamnyobKMHvt1XGKJZcvwzTZOTmsb4eoRG/M9kPjxIAJ63Aj6dCw6mR/+4UWP3orslf7oId61Gd9f0a7f9kuIcxnVUzhGpr1YC16Jbr07o81GPQvNc6vpnPxuYEUGPE9rcC4aFF4uFGzmP931cJNjEVHjMOjGEZNe4hqbvARFfKpjftr6tojRj0gSoncuS/aMBiZFXParnpkEuCWj3FcYcq8s89gxL/agiq5XHjc+P6fk99DSq7ewsAGeU+NIHK0m2orpZWkzJtcC+NUpUtMaabpOml9/fQVBpDXwFZQwzlERJRm1kXzwIDAQAB'

const privateKey = ''

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}

