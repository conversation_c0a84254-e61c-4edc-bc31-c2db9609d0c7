import request from '@/utils/request'

// 查询风险态势趋势文章列表
export function listTrendArticle(query) {
  return request({
    url: '/screen/trendArticle/list',
    method: 'get',
    params: query
  })
}

// 查询风险态势趋势文章详细
export function getTrendArticle(id) {
  return request({
    url: '/screen/trendArticle/' + id,
    method: 'get'
  })
}

// 新增风险态势趋势文章
export function addTrendArticle(data) {
  return request({
    url: '/screen/trendArticle',
    method: 'post',
    data: data
  })
}

// 修改风险态势趋势文章
export function updateTrendArticle(data) {
  return request({
    url: "/screen/trendArticle/edit",
    method: "post",
    data: data,
  });
}

// 删除风险态势趋势文章
export function delTrendArticle(data) {
  return request({
    url: "/screen/trendArticle/remove",
    method: "post",
    data: data,
  });
}
