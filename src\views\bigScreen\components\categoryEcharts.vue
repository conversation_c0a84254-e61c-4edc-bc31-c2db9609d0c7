<template>
  <div class="chartBox">
    <div class="chart" ref="chartContainer"></div>
    <div class="tableList">
      <div v-for="(item, index) in labelList" :style="{ 'color': echartsColorList[index] }" class="tableList-title"
        @mouseenter="showTooltip(index)" @mouseleave="hideTooltip">
        <!-- <el-tooltip class="box-item" effect="dark" :content="String(item.value)" placement="top"> -->
        {{ item.name }}
        <!-- </el-tooltip> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { categoryData } from '../test';

const echartsColorList = ['#61D2FB', '#F8DA58', '#EE7D30', '#E86B6C', '#8F62DD', '#9FD017', '#7EB3FF', '#7CB92C', '#FBA905', '#FA6167']

const props = defineProps({
  sccenId: {
    type: Number,
    default: 1,
  },
  type: {
    type: Number,
    default: 1,
  },
})

const chartContainer = ref(null);
const myChart = ref(null);
const option = ref(null);
const labelList = ref([
  { value: 6010, name: '智能体' },
  { value: 1493, name: 'AI Agent' },
  { value: 1202, name: '思维链' },
  { value: 1202, name: '合成数据' },
  { value: 819, name: '世界模型' },
  { value: 744, name: 'MOE' },
  { value: 708, name: '扩散模型' },
  { value: 477, name: '高质量数据集' },
  { value: 445, name: '小数据' },
  { value: 347, name: 'AI for Science' }
])

onMounted(() => {
  myChart.value = echarts.init(chartContainer.value);
  labelList.value = categoryData['sccen' + props.sccenId + 'type' + props.type]
  initChart()
})

const initChart = () => {
  const label = labelList.value.map(item => item.name)
  const data = labelList.value.map((item, index) => ({ name: item.name, value: item.value, itemStyle: { color: echartsColorList[index] } }))
  const maxValue = Math.max(...labelList.value.map(item => item.value)) + Math.min(...labelList.value.map(item => item.value))

  option.value = {
    // legend: {
    //   show: true,
    //   type: 'plain',
    //   orient: 'vertical',
    //   left: 'right',
    //   top: 'center',
    //   data: label
    // },
    polar: {
      center: ['40%', '50%'],
      radius: [20, '90%']
    },
    angleAxis: {
      max: maxValue,
      startAngle: 90,
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      },
    },
    radiusAxis: {
      type: 'category',
      data: label,
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
    },
    tooltip: {},
    series: {
      type: 'bar',
      data: data,
      coordinateSystem: 'polar',
      barMaxWidth: 10,
      showBackground: true,
    }
  };
  myChart.value.setOption(option.value);
};

const showTooltip = (index) => {
  myChart.value.dispatchAction({
    type: 'showTip',
    seriesIndex: 0,
    dataIndex: index
  });
}

const hideTooltip = () => {
  myChart.value.dispatchAction({
    type: 'hideTip'
  });
}
</script>

<style scoped lang="scss">
.chartBox {
  width: 100%;
  height: 100%;
  position: relative;

  .chart {
    width: 100%;
    height: 100%;
    min-height: 200px;
  }

  .tableList {
    position: absolute;
    display: flex;
    flex-direction: column;
    right: 30px;
    top: 0;
    height: 100%;
    justify-content: center;

    .tableList-title {
      font-size: 14px;
      margin: 1px 0;
      cursor: pointer;
    }
  }
}
</style>