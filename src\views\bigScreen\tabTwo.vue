<template>
  <div style="height: 100%; display: flex" class="two">
    <div class="left">
      <div class="bsContentBox" style="width: 516px; height: 320px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">网络安全技术信息</div>
          <!-- <div class="bsContentTitleHelp"></div> -->
        </div>
        <div class="bsContentContent">
          <myList
            @openArticleDetail="
              (e) => openArticleDetails('wlaq-largeArticle', e)
            "
          ></myList>
        </div>
      </div>
      <div class="bsContentBox" style="width: 516px; height: 320px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">重点事件脉络分析</div>
          <div
            class="bsContentTitleHelp"
            @click="showFirstEvents"
            v-if="eventsShowType == 1"
          ></div>
        </div>
        <div class="bsContentContent">
          <sankTimeLine
            style="width: 516px; height: 277px"
            :timelineEvents="timelineEvents"
            v-if="eventsShowType === 1"
          >
          </sankTimeLine>
          <sankEventsList
            style="width: 516px; height: 277px"
            :dataList="eventsListData"
            @openEventsDetail="openEventsDetail"
            v-else
          ></sankEventsList>
        </div>
      </div>
      <div class="bsContentBox" style="width: 516px; height: 320px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">打压风险</div>
          <div class="bsContentTitleHelp" @click="getRiskDetail"></div>
        </div>
        <div class="bsContentContent">
          <timeLine
            :timelineEvents="suppressListData"
            @openEnterpriseInformation="openEnterpriseInformation"
            style="width: 516px; height: 277px"
          ></timeLine>
        </div>
      </div>
    </div>
    <div class="center">
      <div class="bsContentBox1">
        <div class="bsContentTitle1">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">风险态势</div>
        </div>
        <div class="bsContentContent">
          <lineCharts
            :sccenId="2"
            @openArticleList="openArticleList"
          ></lineCharts>
        </div>
      </div>
      <div class="bsContentBox1" style="height: 320px">
        <div class="bsContentTitle1">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">
            <span
              @click="guanjianType = 1"
              :class="{ titleColor: guanjianType == 1 }"
            >
              关键企业
            </span>
            /
            <span
              @click="guanjianType = 2"
              :class="{ titleColor: guanjianType == 2 }"
            >
              关键人
            </span>
          </div>
        </div>
        <div class="bsContentContent">
          <keyModule
            :screenType="2"
            style="height: 277px"
            :guanjianType="guanjianType"
            @openKeyDetail="openKeyDetail"
          >
          </keyModule>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="bsContentBox2" style="width: 516px; height: 660px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">国内外前沿热点技术</div>
          <div
            class="bsContentTitleHelp"
            @click="comparisonChartShowModal = true"
          ></div>
        </div>
        <div class="bsContentContent" style="height: 208px; margin-top: 10px">
          <technologyArticles
            :sccenId="2"
            @openHotTechnology="openHotTechnology"
          ></technologyArticles>
        </div>
        <div class="bsContentContent" style="padding-top: 0px; height: 397px">
          <graphEcharts
            :sccenId="2"
            @openTechnologyDetails="openTechnologyDetails"
          ></graphEcharts>
        </div>
      </div>
      <div class="bsContentBox" style="width: 516px; height: 320px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">专家观点</div>
          <!-- <div class="bsContentTitleHelp"></div> -->
        </div>
        <div class="bsContentContent">
          <expertOpinions
            :sccenId="2"
            @openArticleDetail="(e) => openArticleDetails('viewpoint', e)"
          ></expertOpinions>
        </div>
      </div>
    </div>

    <analysisOfKeyEvents
      v-model:visible="analysisOfKeyEventsShowModal"
      :title="eventsItem.eventsTitle"
      :detailData="eventsDetailData"
      :timelineData="timelineEvents"
    >
    </analysisOfKeyEvents>
    <articleList
      v-model:visible="articleListShowModal"
      :title="articleListTitle"
      :articles="articleDataList"
      :total="articleDataListTotal"
      @openArticleDetail="(e) => openArticleDetails('analysis-article', e)"
      @pagination="articleListPagination"
    >
    </articleList>
    <suppressionOfRisks
      v-model:visible="suppressionOfRisksShowModal"
      :levelCount="riskBarChartData"
      :enterpriseList="riskEnterpriseList"
      :total="riskEnterpriseListTotal"
      title="打压风险"
      @openEnterpriseInformation="openEnterpriseInformation"
      @pagination="riskEnterpriseListPagination"
    >
    </suppressionOfRisks>
    <keyDetails
      v-model:visible="keyDetailsShowModal"
      :sccenId="2"
      :title="keyDetailsTitle"
      :guanjianType="guanjianType"
      :item="keyDetailsItem"
      :lineChartData="keyDetailLineChartData"
      :articles="keyDetailArticleList"
      :total="keyDetailArticleListTotal"
      @openArticleDetail="(e) => openArticleDetails('keyDetails-article', e)"
      @pagination="keyDetailPagination"
    >
    </keyDetails>
    <technologyDetails
      v-model:visible="technologyDetailsShowModal"
      @openArticleDetail="(e) => openArticleDetails('technology-article', e)"
      :title="technologyDetailsTitle"
      :item="technologyDetailsItem"
    ></technologyDetails>
    <articleDetails
      v-model:visible="articleDetailsShowModal"
      :title="articleDetailsTitle"
      :content="articleDetailsContent"
      :contentEn="articleDetailsContentEn"
      :item="articleDetailsItem"
    >
    </articleDetails>
    <enterpriseInformation
      v-model:visible="enterpriseInformationShowModal"
      :title="enterpriseInformationTitle"
      :content="enterpriseInformationContent"
      :patentList="patentList"
      :softwareList="softwareList"
      :total1="patentTotal"
      :total2="softwareTotal"
      @pagination1="patentPagination"
      @pagination2="softwarePagination"
    >
    </enterpriseInformation>
    <comparisonChart
      v-model:visible="comparisonChartShowModal"
      @openHotTechnology="openHotTechnology"
      title="前沿技术热点对比图详情"
      :sccenId="2"
    ></comparisonChart>
    <hotTechnology
      v-model:visible="hotTechnologyShowModal"
      :title="hotTechnologytTitle"
      :id="hotTechnologytID"
    >
    </hotTechnology>
  </div>
</template>

<script setup>
import myList from "./components/myList";
import sankTimeLine from "./components/sankTimeLine";
import sankEventsList from "./components/eventsList.vue";
import timeLine from "./components/timeLine";
import lineCharts from "./components/lineCharts";
import keyModule from "./components/keyModule";
import graphEcharts from "./components/graphEcharts";
import technologyArticles from "./components/technologyArticles";
import expertOpinions from "./components/expertOpinions";
import analysisOfKeyEvents from "./secondLevel/analysisOfKeyEvents";
import articleList from "./secondLevel/articleList";
import articleDetails from "./secondLevel/articleDetails";
import keyDetails from "./secondLevel/keyDetails";
import suppressionOfRisks from "./secondLevel/suppressionOfRisks";
import enterpriseInformation from "./secondLevel/enterpriseInformation";
import comparisonChart from "./secondLevel/comparisonChart";
import hotTechnology from "./secondLevel/hotTechnology";
import technologyDetails from "./secondLevel/technologyDetails";
import {
  wlaqArticleDetail,
  analysisArticleList,
  analysisArticleDetail,
  enterpriseTrendData,
  enterpriseArticleList,
  enterpriseArticleDetail,
  expertsTrendData,
  expertsArticleList,
  expertsArticleDetail,
  viewpointDetail,
  technicalArticleDetail,
  eventsList,
  eventsNodesData,
  eventsAnalysisData,
  suppressData,
  suppressLevelCount,
  suppressEnterpriseList,
  suppressPatentList,
  suppressSoftwareList,
} from "@/api/bigScreen/index";

const guanjianType = ref(1);
const comparisonChartShowModal = ref(false);
const hotTechnologyShowModal = ref(false);
const hotTechnologytTitle = ref("");
const hotTechnologytID = ref(null);
const technologyDetailsShowModal = ref(false);
const technologyDetailsTitle = ref("");
const technologyDetailsItem = ref(null);
const analysisOfKeyEventsShowModal = ref(false);
const suppressionOfRisksShowModal = ref(false);
const articleListShowModal = ref(false);
const articleListTitle = ref("");
const articleDataList = ref([]);
const articleDataListTotal = ref(0);
const enterpriseInformationShowModal = ref(false);
const enterpriseInformationTitle = ref("");
const articleDetailsShowModal = ref(false);
const articleDetailsTitle = ref("");
const articleDetailsContent = ref("");
const articleDetailsContentEn = ref("");
const keyDetailsShowModal = ref(false);
const keyDetailsTitle = ref("");
const keyDetailsItem = ref({});
const keywords = ref([]);
const drawerInfo = ref({});
const articleDialogVisible1 = ref(false);
const keyDetailLineChartData = ref({});
const keyDetailArticleList = ref([]);
const keyDetailArticleListTotal = ref(0);
const eventsItem = ref({});
const eventsDetailData = ref([]);
const eventsShowType = ref(1);
const eventsListData = ref([]);
const timelineEvents = ref([]);
const suppressListData = ref([]);
const riskBarChartData = ref([]);
const riskEnterpriseList = ref([]);
const riskEnterpriseListTotal = ref(0);
const enterpriseInformationContent = ref([]);
const patentList = ref([]);
const softwareList = ref([]);
const patentTotal = ref(0);
const softwareTotal = ref(0);
const articleListItem = ref({});
const articleDetailsItem = ref({});

// 关键字替换
const changeColor = (str) => {
  let Str = str;
  if (Str) {
    let keywordsArr = keywords.value;
    keywordsArr.map((keyitem, keyindex) => {
      if (keyitem && keyitem.length > 0) {
        // 匹配关键字正则
        let replaceReg = new RegExp(keyitem, "g");
        // 高亮替换v-html值
        let replaceString =
          '<span class="highlight"' +
          ' style="color: #ff7500;">' +
          keyitem +
          "</span>";
        Str = Str.replace(replaceReg, replaceString);
      }
    });
  }
  return Str;
};

const openNewView = async (item) => {
  // await largeCharactersData(item.id).then((res) => {
  //   drawerInfo.value = res.data;
  // });
  let content = drawerInfo.value.article || drawerInfo.value.content;
  if (content) {
    content = content.replace(/\n/g, "<br>");
    content = content.replace(/\${[^}]+}/g, "<br>");
    content = content.replace("|xa0", "");
    content = content.replace("opacity: 0", "");
    content = content.replace(/<img\b[^>]*>/gi, "");
    content = content.replace(/ style="[^"]*"/g, "");
  }
  drawerInfo.value.cnContent = content;
  articleDialogVisible1.value = true;
};

const openArticleList = (item) => {
  articleListItem.value = item;
  analysisArticleList({
    classifySn: item.classifySn,
    pageNum: 1,
    pageSize: 10,
  }).then((res) => {
    articleListTitle.value = item.name;
    articleDataList.value = res.rows;
    articleDataListTotal.value = res.total;
    articleListShowModal.value = true;
  });
};

const articleListPagination = (queryParams) => {
  analysisArticleList({
    classifySn: articleListItem.value.classifySn,
    ...queryParams,
  }).then((res) => {
    articleDataList.value = res.rows;
  });
};

const openArticleDetails = (type, item) => {
  articleDetailsItem.value = item;
  switch (type) {
    case "wlaq-largeArticle":
      wlaqArticleDetail({ id: item.id }).then((res) => {
        articleDetailsTitle.value = item.title;
        articleDetailsContent.value = res.data.content;
        articleDetailsContentEn.value = res.data.enContent;
        articleDetailsShowModal.value = true;
      });
      break;
    case "analysis-article":
      analysisArticleDetail({ id: item.id }).then((res) => {
        articleDetailsTitle.value = item.title;
        articleDetailsContent.value = res.data.content;
        articleDetailsContentEn.value = res.data.enContent;
        articleDetailsShowModal.value = true;
      });
      break;
    case "keyDetails-article":
      if (guanjianType.value == 1) {
        enterpriseArticleDetail({ id: item.id }).then((res) => {
          articleDetailsTitle.value = item.title;
          articleDetailsContent.value = res.data.content;
          articleDetailsContentEn.value = res.data.enContent;
          articleDetailsShowModal.value = true;
        });
      } else if (guanjianType.value == 2) {
        expertsArticleDetail({ id: item.id }).then((res) => {
          articleDetailsTitle.value = item.title;
          articleDetailsContent.value = res.data.content;
          articleDetailsContentEn.value = res.data.enContent;
          articleDetailsShowModal.value = true;
        });
      }
      break;
    case "viewpoint":
      viewpointDetail({ id: item.id }).then((res) => {
        articleDetailsTitle.value = item.title;
        articleDetailsContent.value = res.data.content;
        articleDetailsContentEn.value = res.data.enContent;
        articleDetailsShowModal.value = true;
      });
      break;
    case "technology-article":
      technicalArticleDetail({ id: item.id }).then((res) => {
        articleDetailsTitle.value = item.title;
        articleDetailsContent.value = res.data.content;
        articleDetailsContentEn.value = res.data.enContent;
        articleDetailsShowModal.value = true;
      });
      break;
  }
};

const openKeyDetail = (item) => {
  keyDetailArticleList.value = [];
  keyDetailArticleListTotal.value = 0;
  keyDetailsItem.value = item;
  if (guanjianType.value == 1) {
    enterpriseArticleList({
      enterpriseSn: item.enterpriseSn,
      pageNum: 1,
      pageSize: 10,
    }).then((res) => {
      keyDetailArticleList.value = res.rows;
      keyDetailArticleListTotal.value = res.total;
      keyDetailsTitle.value = item.enterpriseName;
    });
    enterpriseTrendData({
      enterpriseSn: item.enterpriseSn,
    }).then((res) => {
      keyDetailLineChartData.value = {
        xData: res.data.map((item) => item?.publishTime),
        yData: res.data.map((item) => item?.publishCount),
      };
      keyDetailsShowModal.value = true;
    });
  } else if (guanjianType.value == 2) {
    expertsArticleList({
      expertsSn: item.expertsSn,
      pageNum: 1,
      pageSize: 10,
    }).then((res) => {
      keyDetailArticleList.value = res.rows;
      keyDetailArticleListTotal.value = res.total;
      keyDetailsTitle.value = item.expertsName;
    });
    expertsTrendData({
      expertsSn: item.expertsSn,
    }).then((res) => {
      keyDetailLineChartData.value = {
        xData: res.data.map((item) => item?.publishTime),
        yData: res.data.map((item) => item?.publishCount),
      };
      keyDetailsShowModal.value = true;
    });
  }
};

const keyDetailPagination = (queryParams) => {
  if (guanjianType.value == 1) {
    enterpriseArticleList({
      enterpriseSn: keyDetailsItem.value.enterpriseSn,
      ...queryParams,
    }).then((res) => {
      keyDetailArticleList.value = res.rows;
    });
  } else if (guanjianType.value == 2) {
    expertsArticleList({
      expertsSn: keyDetailsItem.value.expertsSn,
      ...queryParams,
    }).then((res) => {
      keyDetailArticleList.value = res.rows;
    });
  }
};

const openEnterpriseInformation = (item) => {
  suppressPatentList({
    suppressSn: item.suppressSn,
    pageNum: 1,
    pageSize: 10,
  }).then((res) => {
    patentList.value = res.rows;
    patentTotal.value = res.total;
  });
  suppressSoftwareList({
    suppressSn: item.suppressSn,
    pageNum: 1,
    pageSize: 10,
  }).then((res) => {
    softwareList.value = res.rows;
    softwareTotal.value = res.total;
  });
  enterpriseInformationContent.value = { ...item };
  enterpriseInformationTitle.value = item.enterpriseName;
  enterpriseInformationShowModal.value = true;
};

const patentPagination = (suppressSn, queryParams) => {
  suppressPatentList({
    suppressSn: suppressSn,
    ...queryParams,
  }).then((res) => {
    patentList.value = res.rows;
  });
};

const softwarePagination = (suppressSn, queryParams) => {
  suppressSoftwareList({
    suppressSn: suppressSn,
    ...queryParams,
  }).then((res) => {
    softwareList.value = res.rows;
  });
};

const getEventsList = async () => {
  try {
    const res = await eventsList({
      projectSn: "1",
      screenSn: "2",
      columnSn: "1",
      pageNum: 1,
      pageSize: 50,
    });
    eventsListData.value = res.rows;
    if (res.rows.length > 1) {
      eventsShowType.value = 2;
    } else {
      eventsNodesData({ eventsSn: res.rows[0].eventsSn }).then((res) => {
        timelineEvents.value = res.data.slice(
          res.data.length - 5 <= 0 ? 0 : res.data.length - 5,
          res.data.length
        );
      });
    }
  } catch (error) {
    console.error("获取重点事件列表失败:", error);
  }
};

const showFirstEvents = () => {
  eventsItem.value = eventsListData.value[0];
  showEventsDetail();
};

const openEventsDetail = (item) => {
  eventsItem.value = item;
  showEventsDetail();
};

const showEventsDetail = () => {
  eventsNodesData({ eventsSn: eventsItem.value.eventsSn }).then((res) => {
    timelineEvents.value = res.data.slice(res.data.length - 5 <= 0 ? 0 : res.data.length - 5, res.data.length);
  });
  eventsAnalysisData({ eventsSn: eventsItem.value.eventsSn }).then((res) => {
    eventsDetailData.value = res.data;
    analysisOfKeyEventsShowModal.value = true;
  });
};

const getSuppressData = () => {
  suppressData({
    projectSn: "1",
    screenSn: "2",
    columnSn: "1",
  }).then((res) => {
    let data = [];
    Object.keys(res.data).forEach((key) => {
      data.push({
        date: key,
        description:
          res.data[key].length <= 3
            ? res.data[key]
            : res.data[key].slice(
                res.data[key].length - 3,
                res.data[key].length
              ),
      });
    });
    suppressListData.value = data.reverse();
  });
};

const getRiskDetail = () => {
  suppressEnterpriseList({
    projectSn: "1",
    screenSn: "2",
    columnSn: "1",
    pageNum: 1,
    pageSize: 10,
  }).then((res) => {
    riskEnterpriseList.value = res.rows.map((item, index) => ({
      ...item,
      type: (index % 3) + 1,
    }));
    riskEnterpriseListTotal.value = res.total;
  });
  suppressLevelCount({
    projectSn: "1",
    screenSn: "2",
    columnSn: "1",
  }).then((res) => {
    // 将对象格式转换为数组格式
    const data = Object.keys(res.data).map((year) => ({
      product: year,
      严重: res.data[year].严重,
      一般: res.data[year].一般,
      较轻: res.data[year].较轻,
    }));
    riskBarChartData.value = data;
    suppressionOfRisksShowModal.value = true;
  });
};

const riskEnterpriseListPagination = (queryParams) => {
  suppressEnterpriseList({
    projectSn: "1",
    screenSn: "2",
    columnSn: "1",
    ...queryParams,
  }).then((res) => {
    riskEnterpriseList.value = res.rows.map((item, index) => ({
      ...item,
      type: (index % 3) + 1,
    }));
  });
};

const openHotTechnology = (data) => {
  hotTechnologyShowModal.value = true;
  hotTechnologytTitle.value = data.title;
  hotTechnologytID.value = data.reportSn;
};
const openTechnologyDetails = (data) => {
  technologyDetailsShowModal.value = true;
  technologyDetailsTitle.value = data.name;
  technologyDetailsItem.value = data.data;
};

getEventsList();
getSuppressData();
</script>

<style lang="scss" scoped>
.two {
  height: 100%;
  width: 100%;
  padding-bottom: 10px;

  .left {
    width: 520px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .center {
    margin: 0 11px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .right {
    width: 520px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .bsContentBox2 {
      background-image: url("@/assets/bigScreen/contentBg1.png");

      .bsContentContent {
        height: calc((100% - 43px) / 2);
      }
    }
  }

  .bsContentBox,
  .bsContentBox2 {
    background-image: url("@/assets/bigScreen/contentBg.png");
    background-size: 100% 100%;

    .bsContentTitle {
      height: 43px;
      display: flex;
      align-items: center;
      padding-left: 10px;

      .bsContentTitleIcon {
        width: 22px;
        height: 22px;
        margin-right: 10px;
        background-image: url("@/assets/bigScreen/titleLogo.png");
        background-size: 100% 100%;
      }

      .bsContentTitleName {
        height: 43px;
        line-height: 43px;
        font-weight: 800;
        font-size: 20px;
        color: #00abf4;
      }

      .bsContentTitleHelp {
        cursor: pointer;
        width: 21px;
        height: 21px;
        margin-left: 10px;
        background-image: url("@/assets/bigScreen/titleHelp.png");
        background-size: 100% 100%;
      }
    }

    .bsContentContent {
      height: calc(100% - 43px);
    }
  }

  .bsContentBox1 {
    .bsContentTitle1 {
      height: 43px;
      display: flex;
      align-items: center;
      padding-left: 10px;
      background-image: url("@/assets/bigScreen/title1.png");
      background-size: 100% 100%;

      .bsContentTitleIcon {
        width: 22px;
        height: 22px;
        margin-right: 10px;
        background-image: url("@/assets/bigScreen/titleLogo.png");
        background-size: 100% 100%;
      }

      .bsContentTitleName {
        height: 43px;
        line-height: 43px;
        font-weight: 800;
        font-size: 20px;
        color: #00abf4;

        span {
          font-weight: normal;
          cursor: pointer;
          color: rgba(0, 171, 244, 0.5);
        }

        .titleColor {
          font-weight: 800;
          color: #ffffff;
        }
      }

      .bsContentTitleHelp {
        cursor: pointer;
        width: 21px;
        height: 21px;
        margin-left: 10px;
        background-image: url("@/assets/bigScreen/titleHelp.png");
        background-size: 100% 100%;
      }
    }

    .bsContentContent {
      height: calc(100% - 43px);
    }
  }
}
</style>
