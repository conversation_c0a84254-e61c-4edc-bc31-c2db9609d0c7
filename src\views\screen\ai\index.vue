<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="唯一标识" prop="aiSn">
        <el-input v-model="queryParams.aiSn" placeholder="请输入唯一标识" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="分类名称" prop="classify">
        <el-input v-model="queryParams.classify" placeholder="请输入分类名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="分类编码" prop="classifySn">
        <el-input v-model="queryParams.classifySn" placeholder="请输入分类编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="租户编码" prop="tenantSn">
        <el-input v-model="queryParams.tenantSn" placeholder="请输入租户编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectSn">
        <el-input v-model="queryParams.projectSn" placeholder="请输入项目编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="屏幕编码" prop="screenSn">
        <el-input v-model="queryParams.screenSn" placeholder="请输入屏幕编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="栏目编码" prop="columnSn">
        <el-input v-model="queryParams.columnSn" placeholder="请输入栏目编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['screen:ai:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['screen:ai:export']">导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          v-hasPermi="['screen:ai:add']">人工智能架构导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImportArticle"
          v-hasPermi="['screen:aiArticle:add']">相关文章导入</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="tableRef" v-if="refreshTable" v-loading="loading" :data="aiList" row-key="aiSn" lazy :load="handleLoad"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column label="分类名称" prop="classify" width="300" show-overflow-tooltip />
      <el-table-column label="唯一标识" prop="aiSn" width="150" />
      <el-table-column label="分类编码" prop="classifySn" width="100" show-overflow-tooltip />
      <el-table-column label="关键词" prop="keywords" width="150" show-overflow-tooltip />
      <el-table-column label="描述" prop="summary" width="300" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="租户编码" align="center" prop="tenantSn" show-overflow-tooltip/>
      <el-table-column label="项目编码" align="center" prop="projectSn" />
      <el-table-column label="屏幕编码" align="center" prop="screenSn" />
      <el-table-column label="栏目编码" align="center" prop="columnSn" />
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="300">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:ai:edit']">修改</el-button>
          <template v-if="scope.row.parentSn == '0'">
            <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)"
              v-hasPermi="['screen:ai:add']">新增</el-button>
          </template>
          <el-button link type="primary" icon="Edit" @click="handleArticle(scope.row)">相关文章
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:ai:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改人工智能架构对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="aiRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="父级分类" prop="parentSn">
          <el-tree-select v-model="form.parentSn" :data="aiOptions"
            :props="{ value: 'aiSn', label: 'classify', children: 'children' }" value-key="aiSn" placeholder="请选择父级分类"
            check-strictly />
        </el-form-item>
        <el-form-item label="唯一标识" prop="aiSn">
          <el-input v-model="form.aiSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="分类名称" prop="classify">
          <el-input v-model="form.classify" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="classifySn">
          <el-input v-model="form.classifySn" placeholder="请输入分类编码" />
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="企业描述" prop="summary">
          <editor :min-height="192" v-model="form.summary" placeholder="请输入内容" />
        </el-form-item>
        <el-row>
          <el-col :span="12" v-if="userStore.roles.includes('admin')">
            <el-form-item label="租户编码" prop="tenantSn">
              <el-input v-model="form.tenantSn" placeholder="请输入租户编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="projectSn">
              <el-input v-model="form.projectSn" placeholder="请输入项目编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕编码" prop="screenSn">
              <el-input v-model="form.screenSn" placeholder="请输入屏幕编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="栏目编码" prop="columnSn">
              <el-input v-model="form.columnSn" placeholder="请输入栏目编码" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <aiArticle v-model:visible="aiArticleVisible" :sn="aiArticleSn" :visibleTitle="aiArticleTitle"
      @closeArticle="closeArticle">
    </aiArticle>
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Ai">
import { getToken } from "@/utils/auth";
import { listAi, getAi, delAi, addAi, updateAi } from "@/api/screen/ai";
import aiArticle from "../secondLevel/aiArticle/index.vue";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();

const { proxy } = getCurrentInstance();

const aiList = ref([]);
const aiOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const refreshTable = ref(true);
const total = ref(0);
const tableRef = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    aiSn: null,
    classify: null,
    classifySn: null,
    parentSn: null,
    keywords: null,
    summary: null,
    status: null,
    userId: null,
    deptId: null,
    deleteTime: null,
    deleteBy: null,
    tenantSn: null,
    projectSn: null,
    screenSn: null,
    columnSn: null
  },
  rules: {
    projectSn: [
      { required: true, message: "项目编码不能为空", trigger: "blur" }
    ],
    // tenantSn: [
    //   { required: true, message: "租户编码不能为空", trigger: "blur" }
    // ],
    screenSn: [
      { required: true, message: "屏幕编码不能为空", trigger: "blur" }
    ],
    columnSn: [
      { required: true, message: "栏目编码不能为空", trigger: "blur" }
    ]
  },
  aiArticleVisible: false,
  aiArticleSn: null,
  aiArticleTitle: '',
});

const { queryParams, form, rules, aiArticleVisible, aiArticleSn, aiArticleTitle } = toRefs(data);

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  businessType: null,
  headers: { Authorization: "Bearer " + getToken() },
  url: ''
});

const aiSet = ref(new Set()); // 使用Set存储表格数据

/** 查询人工智能架构列表 */
function getList() {
  loading.value = true;
  listAi({ ...queryParams.value, aiSn: 0 }).then(response => {
    const newSet = new Set([...aiSet.value, ...response.rows]);
    aiList.value = Array.from(newSet).map(item => {
      item.hasChildren = item.hasChild == 'true';
      return item;
    });
    total.value = response.total;
    loading.value = false;
  });
}

const mapsTableTree = ref(new Map());
const tableTreeRefreshTool = ref({});

// 修改后的懒加载方法
const handleLoad = async (row, treeNode, resolve) => {
  try {
    // 缓存节点信息
    mapsTableTree.value.set(row.aiSn, { tree: row, treeNode, resolve });
    tableTreeRefreshTool.value[row.aiSn] = {
      resolve,
      expandCount: 0,
      prevStatus: false
    };

    const res = await listAi({ aiSn: row.aiSn });
    resolve(res.rows.map(item => {
      item.hasChildren = item.hasChild == 'true';
      return item;
    }));
  } catch (error) {
    console.error('加载失败:', error);
    resolve([]);
  }
};

// 新增刷新方法
const refreshLoadTree = (parentSn) => {
  const nodeInfo = mapsTableTree.value.get(parentSn);
  if (nodeInfo) {
    // 清空原有子节点缓存
    tableRef.value.store.states.lazyTreeNodeMap[parentSn] = [];
    // 重新加载子节点
    handleLoad(nodeInfo.tree, nodeInfo.treeNode, nodeInfo.resolve);
  }
};

// 修改后的提交方法
function submitForm() {
  proxy.$refs["aiRef"].validate(valid => {
    if (valid) {
      const action = form.value.id ? updateAi : addAi;
      action(form.value).then(response => {
        proxy.$modal.msgSuccess(form.value.id ? "修改成功" : "新增成功");
        open.value = false;
        if (form.value.parentSn && form.value.parentSn !== 0) {
          refreshLoadTree(form.value.parentSn);
          const parentNode = aiList.value.find(item => item.aiSn === form.value.parentSn);
          if (parentNode?.parentSn) {
            refreshLoadTree(parentNode.parentSn);
          }
        }
        getList();
      });
    }
  });
}

/** 查询人工智能架构下拉树结构 */
function getTreeselect() {
  listAi({ pageNum: 1, pageSize: 99999, aiSn: 0 }).then(response => {
    aiOptions.value = [];
    const data = { aiSn: 0, classify: '顶级节点', children: [] };
    data.children = proxy.handleTree(response.rows, "aiSn", "parentSn");
    aiOptions.value.push(data);
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    aiSn: null,
    classify: null,
    classifySn: null,
    parentSn: null,
    keywords: null,
    summary: null,
    status: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteTime: null,
    deleteBy: null,
    tenantSn: null,
    projectSn: null,
    screenSn: null,
    columnSn: null
  };
  proxy.resetForm("aiRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  getTreeselect();
  if (row != null && row.aiSn) {
    form.value.parentSn = row.aiSn;
  } else {
    form.value.parentSn = 0;
  }
  open.value = true;
  title.value = "添加人工智能架构";
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  nextTick(() => {
    refreshTable.value = true;
  });
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.parentSn = row.parentSn;
  }
  getAi(row.id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改人工智能架构";
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除人工智能架构编号为"' + row.id + '"的数据项？').then(function () {
    return delAi([row.aiSn]);
  }).then(() => {
    const parentSn = row.parentSn || 0;
    if (tableRef.value.store.states.lazyTreeNodeMap[row.aiSn]) {
      delete tableRef.value.store.states.lazyTreeNodeMap[row.aiSn];
    }
    if (parentSn !== 0) {
      refreshLoadTree(parentSn);
      const parentNode = aiList.value.find(item => item.aiSn === parentSn);
      if (parentNode?.parentSn) {
        refreshLoadTree(parentNode.parentSn);
      }
    }
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

function handleExport() {
  proxy.download('screen/ai/export', {
    ...queryParams.value
  }, `article_${new Date().getTime()}.xlsx`)
}

const handleArticle = (row) => {
  aiArticleSn.value = row.aiSn
  aiArticleTitle.value = row.classify
  aiArticleVisible.value = true
}

const closeArticle = () => {
  aiArticleVisible.value = false
}

function handleImport() {
  upload.title = "人工智能架构导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/ai/excelImport";
  upload.businessType = 100;
  upload.open = true;
};

function handleImportArticle() {
  upload.title = "相关文章导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/aiArticle/excelImport";
  upload.businessType = 101;
  upload.open = true;
};

function importTemplate() {
  proxy.download("xty-screen/ai/downloadTemplate?businessType=" + upload.businessType, {
  }, `${upload.title}模版.xlsx`);
};

const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};

function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};


getList();
</script>
<style scoped lang="scss">
.app-container {
  :deep(.el-popper) {
    max-width: 800px !important;
    line-height: 1.5 !important;
    white-space: normal !important;
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
</style>