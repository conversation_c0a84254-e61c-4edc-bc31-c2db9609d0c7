<template>
  <el-dialog :title="props.visibleTitle" v-model="props.visible" :width="props.visibleWidth" append-to-body
    :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <!-- <el-form-item label="事件编码" prop="eventsSn">
        <el-input v-model="queryParams.eventsSn" placeholder="请输入事件编码" clearable @keyup.enter="handleQuery" />
      </el-form-item> -->
      <el-form-item label="节点时间" prop="nodesTime">
        <el-input v-model="queryParams.nodesTime" placeholder="请输入节点时间" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['screen:eventsNodes:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:eventsNodes:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:eventsNodes:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['screen:eventsNodes:export']">导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="eventsNodesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" prop="eventsSn" width="150" />
      <el-table-column label="事件标题" prop="nodesTitle" min-width="300" show-overflow-tooltip />
      <el-table-column label="事件描述" prop="nodesSummary" min-width="300" show-overflow-tooltip />
      <el-table-column label="节点时间" align="center" prop="nodesTime" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:eventsNodes:edit']">修改
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:eventsNodes:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改热点事件对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="eventsNodesRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="唯一标识" prop="eventsSn">
          <el-input v-model="form.eventsSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="事件标题" prop="nodesTitle">
          <el-input v-model="form.nodesTitle" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="事件描述" prop="nodesSummary">
          <el-input v-model="form.nodesSummary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="节点时间" prop="nodesTime">
          <el-input v-model="form.nodesTime" placeholder="请输入节点时间" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup name="EventsNodes">
import {
  addEventsNodes,
  delEventsNodes,
  getEventsNodes,
  listEventsNodes,
  updateEventsNodes
} from "@/api/screen/eventsNodes";

const emit = defineEmits(['closePatent']);

const { proxy } = getCurrentInstance();

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visibleTitle: {
    type: String,
    default: "自定义弹窗",
  },
  visibleWidth: {
    type: Number,
    default: 1280,
  },
  sn: {
    type: String,
    default: '1',
  }
});

const eventsNodesList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    eventsSn: null,
    nodesTitle: null,
    nodesTime: null,
    nodesKeywords: null,
    nodesSummary: null,
    cover: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null
  },
  rules: {
    nodesTitle: [
      { required: true, message: "事件标题不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.visible,
  (newValue, oldValue) => {
    if (newValue) {
      data.queryParams.eventsSn = props.sn;
      getList();
    }
  }
);

/** 查询热点事件列表 */
function getList() {
  loading.value = true;
  listEventsNodes(queryParams.value).then(response => {
    eventsNodesList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    eventsSn: null,
    nodesTitle: null,
    nodesTime: null,
    nodesKeywords: null,
    nodesSummary: null,
    cover: null,
    status: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    delFlag: null,
    deleteBy: null,
    deleteTime: null
  };
  proxy.resetForm("eventsNodesRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  data.form.eventsSn = props.sn
  title.value = "添加热点事件";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getEventsNodes(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改热点事件";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["eventsNodesRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateEventsNodes(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addEventsNodes(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  proxy.$modal.confirm('是否确认删除热点事件编号为"' + _ids + '"的数据项？').then(function () {
    return delEventsNodes(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/eventsNodes/export', {
    ...queryParams.value
  }, `eventsNodes_${new Date().getTime()}.xlsx`)
}

const handleClose = (done) => {
  emit('closeNodes', false);
  done()
}

getList();
</script>
