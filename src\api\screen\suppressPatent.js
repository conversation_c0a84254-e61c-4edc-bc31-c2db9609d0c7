import request from '@/utils/request'

// 查询大屏-打压风险-受压企业专利列表
export function listSuppressPatent(query) {
  return request({
    url: '/screen/suppressPatent/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-打压风险-受压企业专利详细
export function getSuppressPatent(id) {
  return request({
    url: '/screen/suppressPatent/' + id,
    method: 'get'
  })
}

// 新增大屏-打压风险-受压企业专利
export function addSuppressPatent(data) {
  return request({
    url: '/screen/suppressPatent',
    method: 'post',
    data: data
  })
}

// 修改大屏-打压风险-受压企业专利
export function updateSuppressPatent(data) {
  return request({
    url: '/screen/suppressPatent/edit',
    method: 'post',
    data: data
  })
}

// 删除大屏-打压风险-受压企业专利
export function delSuppressPatent(data) {
  return request({
    url: '/screen/suppressPatent/remove',
    method: 'post',
    data: data
  })
}
