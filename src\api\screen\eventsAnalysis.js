import request from '@/utils/request'

// 查询事件分析列表
export function listEventsAnalysis(query) {
  return request({
    url: '/screen/eventsAnalysis/list',
    method: 'get',
    params: query
  })
}

// 查询事件分析详细
export function getEventsAnalysis(id) {
  return request({
    url: '/screen/eventsAnalysis/' + id,
    method: 'get'
  })
}

// 新增事件分析
export function addEventsAnalysis(data) {
  return request({
    url: '/screen/eventsAnalysis',
    method: 'post',
    data: data
  })
}

// 修改事件分析
export function updateEventsAnalysis(data) {
  return request({
    url: '/screen/eventsAnalysis',
    method: 'post',
    data: data
  })
}

// 删除事件分析
export function delEventsAnalysis(data) {
  return request({
    url: '/screen/eventsAnalysis/remove',
    method: 'post',
    data: data
  })
}
