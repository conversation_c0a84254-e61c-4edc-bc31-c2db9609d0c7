import request from '@/utils/request'

// 查询大屏-热门文章列表
export function listArticle(query) {
  return request({
    url: '/screen/article/list',
    method: 'get',
    params: query
  })
}

// 查询大屏-热门文章详细
export function getArticle(id) {
  return request({
    url: '/screen/article/' + id,
    method: 'get'
  })
}

// 新增大屏-热门文章
export function addArticle(data) {
  return request({
    url: '/screen/article',
    method: 'post',
    data: data
  })
}

// 修改大屏-热门文章
export function updateArticle(data) {
  return request({
    url: '/screen/article/edit',
    method: 'post',
    data: data
  })
}

// 删除大屏-热门文章
export function delArticle(data) {
  return request({
    url: '/screen/article/remove',
    method: 'post',
    data: data
  })
}
