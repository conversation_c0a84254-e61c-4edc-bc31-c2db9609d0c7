<template>
  <el-dialog :title="props.visibleTitle" v-model="props.visible" :width="props.visibleWidth" append-to-body
    :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="文章唯一标识" prop="sn">
        <el-input v-model="queryParams.sn" placeholder="请输入文章唯一标识" clearable />
      </el-form-item>
      <el-form-item label="数据源编码" prop="sourceSn">
        <el-input v-model="queryParams.sourceSn" placeholder="请输入数据源编码" clearable />
      </el-form-item>
      <el-form-item label="数据源名称" prop="sourceName">
        <el-input v-model="queryParams.sourceName" placeholder="请输入数据源名称" clearable />
      </el-form-item>
      <el-form-item label="热门时间" prop="publishTime">
        <el-date-picker clearable v-model="queryParams.publishTime" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择热门时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['screen:technicalArticle:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:technicalArticle:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:technicalArticle:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['screen:technicalArticle:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="technicalArticleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" prop="technicalSn" width="150" />
      <el-table-column label="文章唯一标识" prop="sn" width="150" />
      <el-table-column label="文章标题" prop="title" width="300" show-overflow-tooltip />
      <el-table-column label="数据源编码" prop="sourceSn" width="300" show-overflow-tooltip />
      <el-table-column label="数据源名称" prop="sourceName" width="300" show-overflow-tooltip />
      <el-table-column label="官网连接" prop="originalUrl" width="300" show-overflow-tooltip />
      <el-table-column label="热门时间" align="center" prop="publishTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关键词" prop="keywords" width="300" show-overflow-tooltip />
      <el-table-column label="企业描述" prop="summary" width="300" show-overflow-tooltip />
      <!-- <el-table-column label="文章内容" prop="content" width="300" show-overflow-tooltip /> -->
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status == '0' ? 'success' : 'danger'">{{
            scope.row.status == '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" min-width="160">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:technicalArticle:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:technicalArticle:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改前沿技术-文章对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="technicalArticleRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="唯一标识" prop="technicalSn">
          <el-input v-model="form.technicalSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="文章唯一标识" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入文章唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="文章标题" prop="title">
          <el-input v-model="form.title" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="数据源编码" prop="sourceSn">
          <el-input v-model="form.sourceSn" placeholder="请输入数据源编码" />
        </el-form-item>
        <el-form-item label="数据源名称" prop="sourceName">
          <el-input v-model="form.sourceName" placeholder="请输入数据源名称" />
        </el-form-item>
        <el-form-item label="官网连接" prop="originalUrl">
          <el-input v-model="form.originalUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="热门时间" prop="publishTime">
          <el-date-picker clearable v-model="form.publishTime" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择热门时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="企业描述" prop="summary">
          <el-input v-model="form.summary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="文章内容">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup name="TechnicalArticle">
import { listTechnicalArticle, getTechnicalArticle, delTechnicalArticle, addTechnicalArticle, updateTechnicalArticle } from "@/api/screen/technicalArticle";

const emit = defineEmits(['closeArticle']);

const { proxy } = getCurrentInstance();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visibleTitle: {
    type: String,
    default: "自定义弹窗",
  },
  visibleWidth: {
    type: Number,
    default: 1280,
  },
  sn: {
    type: String,
    default: '1',
  }
});

const technicalArticleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    technicalSn: null,
    sn: null,
    title: null,
    sourceSn: null,
    sourceName: null,
    originalUrl: null,
    publishTime: null,
    keywords: null,
    summary: null,
    content: null,
    author: null,
    orderNum: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.visible,
  (newValue, oldValue) => {
    if (newValue) {
      data.queryParams.technicalSn = props.sn;
      getList();
    }
  }
);

/** 查询前沿技术-文章列表 */
function getList() {
  loading.value = true;
  listTechnicalArticle(queryParams.value).then(response => {
    technicalArticleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    technicalSn: null,
    sn: null,
    title: null,
    sourceSn: null,
    sourceName: null,
    originalUrl: null,
    publishTime: null,
    keywords: null,
    summary: null,
    content: null,
    author: null,
    orderNum: null,
    status: null,
    delFlag: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null
  };
  proxy.resetForm("technicalArticleRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  data.form.technicalSn = props.sn
  title.value = "添加前沿技术-文章";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getTechnicalArticle(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改前沿技术-文章";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["technicalArticleRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateTechnicalArticle(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTechnicalArticle(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  proxy.$modal.confirm('是否确认删除前沿技术-文章编号为"' + _ids + '"的数据项？').then(function () {
    return delTechnicalArticle(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/technicalArticle/export', {
    ...queryParams.value
  }, `technicalArticle_${new Date().getTime()}.xlsx`)
}

const handleClose = (done) => {
  emit('closeArticle', false);
  done()
}

</script>
