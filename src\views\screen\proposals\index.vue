<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="唯一标识" prop="proposalsSn">
        <el-input v-model="queryParams.proposalsSn" placeholder="请输入唯一标识" clearable />
      </el-form-item>
      <el-form-item label="提案时间" prop="publishTime">
        <el-date-picker clearable v-model="queryParams.publishTime" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择提案时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label-width="120px" label="是否与中国有关" prop="proposalsToChina">
        <el-select v-model="queryParams.proposalsToChina" placeholder="请选择是否与中国有关" style="width: 200px;">
          <el-option label="无关" value="0" />
          <el-option label="有关" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目编码" prop="projectSn">
        <el-input v-model="queryParams.projectSn" placeholder="请输入项目编码" clearable />
      </el-form-item>
      <el-form-item label="屏幕编码" prop="screenSn">
        <el-input v-model="queryParams.screenSn" placeholder="请输入屏幕编码" clearable />
      </el-form-item>
      <el-form-item label="栏目编码" prop="columnSn">
        <el-input v-model="queryParams.columnSn" placeholder="请输入栏目编码" clearable />
      </el-form-item>
      <el-form-item label="租户编码" prop="tenantSn">
        <el-input v-model="queryParams.tenantSn" placeholder="请输入租户编码" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['screen:proposals:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:proposals:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:proposals:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['screen:proposals:export']">导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport"
          v-hasPermi="['screen:proposals:add']">政策提案导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImportPeople"
          v-hasPermi="['screen:proposals:add']">政策提案关联人员导入</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="proposalsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" align="center" prop="proposalsSn" width="150" />
      <el-table-column label="提案时间" align="center" prop="publishTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提案标题" prop="proposalsTitle" width="300" show-overflow-tooltip />
      <el-table-column label="提案影响" prop="proposalsEffect" width="300" show-overflow-tooltip />
      <el-table-column label="是否与中国有关" align="center" prop="proposalsToChina" width="160">
        <template #default="scope">
          <span>{{ scope.row.proposalsToChina == 0 ? '无关' : scope.row.proposalsToChina == 1 ? '有关' : '' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="提案内容" prop="proposalsContent" width="300" show-overflow-tooltip /> -->
      <!-- <el-table-column label="提案英文内容" prop="proposalsEnContent" width="300" show-overflow-tooltip /> -->
      <el-table-column label="关键词" prop="keywords" width="300" show-overflow-tooltip />
      <el-table-column label="提案摘要" prop="summary" width="300" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目编码" align="center" prop="projectSn" />
      <el-table-column label="屏幕编码" align="center" prop="screenSn" />
      <el-table-column label="栏目编码" align="center" prop="columnSn" />
      <el-table-column label="租户编码" align="center" prop="tenantSn" show-overflow-tooltip/>
      <el-table-column label="操作" align="center" fixed="right" min-width="160" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:proposals:edit']">修改
          </el-button>
          <!-- <el-button link type="primary" icon="Edit" @click="handleProposalsExperts(scope.row)">提案人员
          </el-button> -->
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:proposals:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改政策提案对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="proposalsRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="唯一标识" prop="proposalsSn">
          <el-input v-model="form.proposalsSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="提案时间" prop="publishTime">
          <el-date-picker clearable v-model="form.publishTime" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择提案时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="提案标题" prop="proposalsTitle">
          <el-input v-model="form.proposalsTitle" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="提案人员" prop="experts">
          <el-select v-model="form.experts" placeholder="请选择提案人员" multiple collapse-tags collapse-tags-tooltip
            :max-collapse-tags="5" filterable>
            <el-option v-for="item in proposalsExpertsList" :label="item.proposalsExperts"
              :value="item.expertsSn"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提案影响" prop="proposalsEffect">
          <editor v-model="form.proposalsEffect" placeholder="请输入内容" :min-height="192" />
        </el-form-item>
        <el-form-item label="与中国有关" prop="proposalsToChina">
          <el-select v-model="form.proposalsToChina" placeholder="请选择是否与中国有关">
            <el-option label="无关" :value="'0'"></el-option>
            <el-option label="有关" :value="'1'"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提案内容" prop="proposalsContent">
          <editor v-model="form.proposalsContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="英文内容" prop="proposalsEnContent">
          <editor v-model="form.proposalsEnContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="提案摘要" prop="summary">
          <editor v-model="form.summary" placeholder="请输入内容" :min-height="192" />
        </el-form-item>
        <!-- <el-form-item label="文章唯一标识" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入文章唯一标识" />
        </el-form-item> -->
        <el-row>
          <el-col :span="12" v-if="userStore.roles.includes('admin')">
            <el-form-item label="租户编码" prop="tenantSn">
              <el-input v-model="form.tenantSn" placeholder="请输入租户编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编码" prop="projectSn">
              <el-input v-model="form.projectSn" placeholder="请输入项目编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="屏幕编码" prop="screenSn">
              <el-input v-model="form.screenSn" placeholder="请输入屏幕编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="栏目编码" prop="columnSn">
              <el-input v-model="form.columnSn" placeholder="请输入栏目编码" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <proposalsExperts v-model:visible="proposalsExpertsVisible" :sn="proposalsExpertsSn"
      :visibleTitle="proposalsExpertsTitle" @closeProposalsExperts="closeProposalsExperts">
    </proposalsExperts>
  </div>
</template>

<script setup name="Proposals">
import { getToken } from "@/utils/auth";
import { addProposals, delProposals, getProposals, listProposals, updateProposals } from "@/api/screen/proposals";
import { listProposalsExperts } from "@/api/screen/proposalsExperts";
import proposalsExperts from "../secondLevel/proposalsExperts/index.vue";
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
const { proxy } = getCurrentInstance();

const proposalsList = ref([]);
const proposalsExpertsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    proposalsSn: null,
    publishTime: null,
    proposalsTitle: null,
    proposalsEffect: null,
    proposalsToChina: null,
    proposalsContent: null,
    proposalsEnContent: null,
    keywords: null,
    summary: null,
    sn: null,
    orderNum: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    projectSn: null,
    screenSn: null,
    columnSn: null,
    tenantSn: null
  },
  rules: {
    projectSn: [
      { required: true, message: "项目编码不能为空", trigger: "blur" }
    ],
    // tenantSn: [
    //   { required: true, message: "租户编码不能为空", trigger: "blur" }
    // ],
    screenSn: [
      { required: true, message: "屏幕编码不能为空", trigger: "blur" }
    ],
    columnSn: [
      { required: true, message: "栏目编码不能为空", trigger: "blur" }
    ]
  },
  proposalsExpertsVisible: false,
  proposalsExpertsSn: null,
  proposalsExpertsTitle: '',
});

const { queryParams, form, rules, proposalsExpertsVisible, proposalsExpertsSn, proposalsExpertsTitle, } = toRefs(data);

const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  businessType: null,
  headers: { Authorization: "Bearer " + getToken() },
  url: ''
});

/** 查询政策提案列表 */
function getList() {
  loading.value = true;
  listProposals(queryParams.value).then(response => {
    proposalsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

listProposalsExperts({ pageSize: 9999, pageNum: 1 }).then(response => {
  proposalsExpertsList.value = response.rows;
});

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    proposalsSn: null,
    publishTime: null,
    proposalsTitle: null,
    proposalsEffect: null,
    proposalsToChina: null,
    proposalsContent: null,
    proposalsEnContent: null,
    keywords: null,
    summary: null,
    sn: null,
    orderNum: null,
    status: null,
    delFlag: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null,
    projectSn: null,
    screenSn: null,
    columnSn: null,
    tenantSn: null
  };
  proxy.resetForm("proposalsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加政策提案";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getProposals(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改政策提案";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["proposalsRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateProposals(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addProposals(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  _ids = _ids.flatMap(item => {
    const found = proposalsList.value.find(row => row.id === item)
    return found?.proposalsSn ? [found.proposalsSn] : []
  })
  proxy.$modal.confirm('是否确认删除政策提案编号为"' + _ids + '"的数据项？').then(function () {
    return delProposals(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/proposals/export', {
    ...queryParams.value
  }, `proposals_${new Date().getTime()}.xlsx`)
}

const handleProposalsExperts = (row) => {
  proposalsExpertsSn.value = row.proposalsSn
  proposalsExpertsTitle.value = row.proposalsTitle
  proposalsExpertsVisible.value = true
}

const closeProposalsExperts = () => {
  proposalsExpertsVisible.value = false
}

/** 政策提案导入 */
function handleImport() {
  upload.title = "政策提案导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/proposals/excelImport";
  upload.businessType = 100;
  upload.open = true;
};

/** 政策提案人员导入 */
function handleImportPeople() {
  upload.title = "政策提案关联人员导入";
  upload.url = import.meta.env.VITE_APP_BASE_API + "/xty-screen/proposals/experts/excelImport";
  upload.businessType = 101;
  upload.open = true;
};

/** 下载模板操作 */
function importTemplate() {
  proxy.download("xty-screen/proposals/downloadTemplate?businessType=" + upload.businessType, {
  }, `${upload.title}模版.xlsx`);
};

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};


getList();
</script>
<style scoped lang="scss">
.app-container {
  :deep(.el-popper) {
    max-width: 800px !important;
    line-height: 1.5 !important;
    white-space: normal !important;
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
</style>