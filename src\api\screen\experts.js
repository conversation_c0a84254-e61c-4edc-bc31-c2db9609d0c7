import request from "@/utils/request";

// 查询关键人列表
export function listExperts(query) {
  return request({
    url: "/screen/experts/list",
    method: "get",
    params: query,
  });
}

// 查询关键人详细
export function getExperts(id) {
  return request({
    url: "/screen/experts/" + id,
    method: "get",
  });
}

// 新增关键人
export function addExperts(data) {
  return request({
    url: "/screen/experts",
    method: "post",
    data: data,
  });
}

// 修改关键人
export function updateExperts(data) {
  return request({
    url: "/screen/experts/edit",
    method: "post",
    data: data,
  });
}

// 删除关键人
export function delExperts(data) {
  return request({
    url: "/screen/experts/remove",
    method: "post",
    data: data,
  });
}

// 查询关键人趋势数据列表
export function listExpertsTrend(query) {
  return request({
    url: "/screen/expertsTrend/list",
    method: "get",
    params: query,
  });
}

// 查询关键人趋势数据详细
export function getExpertsTrend(id) {
  return request({
    url: "/screen/expertsTrend/" + id,
    method: "get",
  });
}

// 新增关键人趋势数据
export function addExpertsTrend(data) {
  return request({
    url: "/screen/expertsTrend",
    method: "post",
    data: data,
  });
}

// 修改关键人趋势数据
export function updateExpertsTrend(data) {
  return request({
    url: "/screen/expertsTrend/edit",
    method: "post",
    data: data,
  });
}

// 删除关键人趋势数据
export function delExpertsTrend(data) {
  return request({
    url: "/screen/expertsTrend/remove",
    method: "post",
    data: data,
  });
}

// 查询关键人文章列表
export function listExpertsArticle(query) {
  return request({
    url: "/screen/expertsArticle/list",
    method: "get",
    params: query,
  });
}

// 查询关键人文章详细
export function getExpertsArticle(id) {
  return request({
    url: "/screen/expertsArticle/" + id,
    method: "get",
  });
}

// 新增关键人文章
export function addExpertsArticle(data) {
  return request({
    url: "/screen/expertsArticle",
    method: "post",
    data: data,
  });
}

// 修改关键人文章
export function updateExpertsArticle(data) {
  return request({
    url: "/screen/expertsArticle/edit",
    method: "post",
    data: data,
  });
}

// 删除关键人文章
export function delExpertsArticle(data) {
  return request({
    url: "/screen/expertsArticle/remove",
    method: "post",
    data: data,
  });
}
