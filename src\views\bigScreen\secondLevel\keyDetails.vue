<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div class="custom-dialog" :style="{ width: props.width + 'px' }" @click.stop>
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div class="custom-dialog-close" @click="closeDialog"></div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">
            {{ props.guanjianType == 1 ? "企业介绍" : "人物介绍" }}
          </div>
          <div class="bg-box-content flex-box">
            <div class="bg-box-img" v-if="props.guanjianType == 2">
              <img :src="baseUrl + props.item.avatar" alt="" />
            </div>
            <div class="bg-box-summary" :style="{ 'margin-left': props.guanjianType == 2 ? '20px' : '' }" v-html="
              props.guanjianType == 1
                ? props.item.summary
                : props.item.profile
            "></div>
          </div>
        </div>
        <div class="bg-box" v-if="!(props.guanjianType == 2 && props.sccenId == 2)">
          <div class="bg-box-title">内容趋势</div>
          <div class="bg-box-content">
            <div ref="chartKeyDetails" style="width: 100%; height: 300px"></div>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">相关内容</div>
          <div class="bg-box-content">
            <ul class="article-list" v-if="articles.length > 0">
              <li v-for="article in articles" :key="article.id" class="article-item">
                <div class="article-title" @click="openDetail(article)">
                  {{ article.title }}
                </div>
                <div class="article-publishTime">{{ article.publishTime }}</div>
              </li>
            </ul>
            <div v-else>
              <div class="no-data">暂无数据</div>
            </div>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize" @pagination="getList" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { defineProps, defineEmits, computed, watch, nextTick } from "vue";
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  closeOnClickMask: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 1200,
  },
  item: {
    type: Object,
    default: () => ({}),
  },
  sccenId: {
    type: Number,
    default: 1,
  },
  guanjianType: {
    type: Number,
    default: 1,
  },
  articles: {
    type: Array,
    default: () => [],
  },
  lineChartData: {
    type: Object,
    default: () => ({}),
  },
  total: {
    type: Number,
    default: 0,
  },
});
const emits = defineEmits([
  "update:visible",
  "openArticleDetail",
  "pagination",
]);

var myChart = null;
const option = ref({});
const chartKeyDetails = ref(null);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);

watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      queryParams.value.pageNum = 1;
      nextTick(() => {
        if (chartKeyDetails.value) {
          myChart = echarts.init(chartKeyDetails.value);
          initChart();
        }
      });
    }
  }
);

const initChart = () => {
  const valueObj = computed(() => {
    return {
      xData: props.lineChartData.xData,
      yData: props.lineChartData.yData,
    };
  });
  option.value = {
    tooltip: {
      show: true,
      trigger: "axis",
      showContent: true,
      triggerOn: "mousemove",
      // axisPointer: {
      //   type: "shadow",
      // },
    },
    grid: {
      left: "0%",
      right: "0%",
      bottom: "0%",
      top: "5%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: valueObj.value.xData,
      axisLabel: {
        fontSize: "14px",
        color: "#fff",
        formatter: function (value) {
          if (value.length > 8) {
            return `${value.slice(0, 8)}...`;
          }
          return value;
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        show: true,
        lineStyle: {
          color: "#ffffff70",
          type: "dotted",
        },
      },
      axisLabel: {
        interval: 0,
        fontSize: "14px",
        color: "#fff",
      },
    },
    series: [
      {
        data: valueObj.value.yData,
        type: "line",
        smooth: true,
      },
    ],
  };

  setTimeout(() => {
    myChart?.resize();
  }, 1);
  myChart.setOption(option.value);
};

const openDetail = (item) => {
  emits("openArticleDetail", item);
};

const getList = () => {
  emits("pagination", queryParams.value);
};

// 关闭弹窗的方法
const closeDialog = () => {
  emits("update:visible", false);
};

// 处理遮罩层点击事件
const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    closeDialog();
  }
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("@/assets/bigScreen/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("@/assets/bigScreen/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("@/assets/bigScreen/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      max-height: 80vh;
      overflow: auto;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          .bg-box-img {
            width: 100px;
            height: 120px;
            background: #fff;
            vertical-align: middle;
            position: relative;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .bg-box-summary {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
          }
        }

        .flex-box {
          display: flex;
        }
      }

      .article-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .article-item {
          display: flex;
          height: 40px;
          line-height: 40px;
          margin-bottom: 10px;
          font-size: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .article-title {
            cursor: pointer;
            width: calc(100% - 100px);
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .article-publishTime {
            width: 100px;
            color: #ccc;
          }
        }
      }
    }
  }
}

.no-data {
  text-align: center;
  color: #fff;
  font-size: 16px;
  height: 100px;
  line-height: 80px;
}

:deep(.pagination-container) {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump) {
  color: #f2f2f2;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination button:disabled) {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

:deep(.el-pager li) {
  background: #ffffff00 !important;
  color: #fff !important;

  &.is-active {
    color: #1890ff !important;
  }
}
</style>
