import request from '@/utils/request'

// 查询前沿技术热点趋势列表
export function listTechnicalTrend(query) {
  return request({
    url: '/screen/technicalTrend/list',
    method: 'get',
    params: query
  })
}

// 查询前沿技术热点趋势详细
export function getTechnicalTrend(id) {
  return request({
    url: '/screen/technicalTrend/' + id,
    method: 'get'
  })
}

// 新增前沿技术热点趋势
export function addTechnicalTrend(data) {
  return request({
    url: '/screen/technicalTrend',
    method: 'post',
    data: data
  })
}

// 修改前沿技术热点趋势
export function updateTechnicalTrend(data) {
  return request({
    url: '/screen/technicalTrend/edit',
    method: 'post',
    data: data
  })
}

// 删除前沿技术热点趋势
export function delTechnicalTrend(data) {
  return request({
    url: '/screen/technicalTrend/remove',
    method: 'post',
    data: data
  })
}
