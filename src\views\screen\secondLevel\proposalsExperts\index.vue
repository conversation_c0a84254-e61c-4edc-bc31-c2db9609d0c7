<template>
  <el-dialog :title="props.visibleTitle" v-model="props.visible" :width="props.visibleWidth" append-to-body
    :before-close="handleClose" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="唯一标识" prop="expertsSn">
        <el-input v-model="queryParams.expertsSn" placeholder="请输入唯一标识" clearable />
      </el-form-item>
      <el-form-item label="提案人员" prop="proposalsExperts">
        <el-input v-model="queryParams.proposalsExperts" placeholder="请输入提案人员" clearable />
      </el-form-item>
      <el-form-item label="所属党派" prop="belongToGroup">
        <el-input v-model="queryParams.belongToGroup" placeholder="请输入所属党派" clearable />
      </el-form-item>
      <el-form-item label="所属地区" prop="belongToArea">
        <el-input v-model="queryParams.belongToArea" placeholder="请输入所属地区" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['screen:proposalsExperts:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['screen:proposalsExperts:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['screen:proposalsExperts:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['screen:proposalsExperts:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="proposalsExpertsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="唯一标识" prop="expertsSn" width="150" />
      <el-table-column label="提案人员" prop="proposalsExperts" width="300" show-overflow-tooltip />
      <el-table-column label="人员照片" align="center" prop="proposalsExpertsCover">
        <template #default="scope">
          <img :src="baseUrl + scope.row.proposalsExpertsCover" style="width: 100px; height: 120px; object-fit: cover" />
        </template>
      </el-table-column>
      <el-table-column label="所属党派" align="center" prop="belongToGroup" width="150" show-overflow-tooltip />
      <el-table-column label="所属地区" prop="belongToArea" width="150" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{
            scope.row.status === '0' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['screen:proposalsExperts:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['screen:proposalsExperts:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改政策提案人员对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="proposalsExpertsRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="唯一标识" prop="expertsSn">
          <el-input v-model="form.expertsSn" placeholder="请输入唯一标识" :disabled="true" />
        </el-form-item>
        <el-form-item label="提案人员" prop="proposalsExperts">
          <el-input v-model="form.proposalsExperts" placeholder="请输入提案人员" />
        </el-form-item>
        <el-form-item label="人员照片" prop="proposalsExpertsCover">
          <image-upload v-model="form.proposalsExpertsCover" :limit="1" />
        </el-form-item>
        <el-form-item label="所属党派" prop="belongToGroup">
          <el-input v-model="form.belongToGroup" placeholder="请输入所属党派" />
        </el-form-item>
        <el-form-item label="所属地区" prop="belongToArea">
          <el-input v-model="form.belongToArea" placeholder="请输入所属地区" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup name="ProposalsExperts">
import { listProposalsExperts, getProposalsExperts, delProposalsExperts, addProposalsExperts, updateProposalsExperts } from "@/api/screen/proposalsExperts";

const emit = defineEmits(['closeProposalsExperts']);

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visibleTitle: {
    type: String,
    default: "自定义弹窗",
  },
  visibleWidth: {
    type: Number,
    default: 1280,
  },
  sn: {
    type: String,
    default: '1',
  }
});

const { proxy } = getCurrentInstance();
const baseUrl = import.meta.env.VITE_BASE_API;

const proposalsExpertsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    expertsSn: null,
    proposalsExperts: null,
    proposalsExpertsCover: null,
    belongToGroup: null,
    belongToArea: null,
    orderNum: null,
    status: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

watch(
  () => props.visible,
  (newValue, oldValue) => {
    if (newValue) {
      data.queryParams.suppressSn = props.sn;
      getList();
    }
  }
);

/** 查询政策提案人员列表 */
function getList() {
  loading.value = true;
  listProposalsExperts(queryParams.value).then(response => {
    proposalsExpertsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    expertsSn: null,
    proposalsExperts: null,
    proposalsExpertsCover: null,
    belongToGroup: null,
    belongToArea: null,
    orderNum: null,
    status: null,
    delFlag: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    userId: null,
    deptId: null,
    deleteBy: null,
    deleteTime: null
  };
  proxy.resetForm("proposalsExpertsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  data.form.suppressSn = props.sn;
  title.value = "添加政策提案人员";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getProposalsExperts(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改政策提案人员";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["proposalsExpertsRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateProposalsExperts(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addProposalsExperts(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  let _ids
  if (row.id) {
    _ids = [row.id]
  } else {
    _ids = ids.value
  }
  proxy.$modal.confirm('是否确认删除政策提案人员编号为"' + _ids + '"的数据项？').then(function () {
    return delProposalsExperts(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('screen/proposalsExperts/export', {
    ...queryParams.value
  }, `proposalsExperts_${new Date().getTime()}.xlsx`)
}

const handleClose = (done) => {
  emit('closeProposalsExperts', false);
  done()
}
</script>
